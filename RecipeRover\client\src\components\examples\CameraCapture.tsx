import CameraCapture from '../CameraCapture'

export default function CameraCaptureExample() {
  const handleImageCapture = (imageData: string) => {
    console.log('Image captured:', imageData.slice(0, 50) + '...');
  };

  const handleVideoFrame = (imageData: string) => {
    console.log('Video frame captured for analysis:', imageData.slice(0, 50) + '...');
  };

  return (
    <CameraCapture 
      onImageCapture={handleImageCapture}
      onVideoFrame={handleVideoFrame}
      exercise="Squat"
      isAnalyzing={false}
    />
  );
}