sudo: false

language: node_js

node_js:
  - "0.8"
  - "0.10"
  - "0.12"
  - "1"
  - "2"
  - "3"
  - "4"
  - "5"
  - "6"
  - "7"
  - "8"

install:
  - PATH="`npm bin`:`npm bin -g`:$PATH"
  # Node 0.8 comes with a too obsolete npm
  - if [[ "`node --version`" =~ ^v0\.8\. ]]; then npm install -g npm@1.4.28 ; fi
  # Install dependencies and build
  - npm install

script:
  # Output useful info for debugging
  - node --version
  - npm --version
  # Run tests
  - npm test
