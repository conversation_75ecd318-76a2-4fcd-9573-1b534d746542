import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Area,
  AreaChart
} from "recharts";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Skeleton } from "@/components/ui/skeleton";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Calendar,
  TrendingUp,
  Trophy,
  Target,
  Activity,
  Clock,
  Flame,
  Award,
  BarChart3,
  LineChart as LineChartIcon,
  <PERSON><PERSON><PERSON> as PieChartIcon,
  Apple,
  Utensils,
  Scale
} from "lucide-react";
import { getCurrentUserId } from "@/lib/currentUser";
import { apiRequest } from "@/lib/queryClient";
import type {
  AnalyticsOverview,
  WorkoutTrend,
  VolumeProgression,
  PersonalRecord,
  MuscleGroupDistribution,
  GoalProgress,
  Achievement
} from "@shared/schema";

// Analytics types now imported from shared schema

type TimeRange = '1w' | '1m' | '3m' | '1y';

const timeRangeOptions = {
  '1w': { label: 'Last Week', days: 7 },
  '1m': { label: 'Last Month', days: 30 },
  '3m': { label: 'Last 3 Months', days: 90 },
  '1y': { label: 'Last Year', days: 365 }
};

// Chart color scheme matching Circuit branding
const COLORS = {
  primary: 'hsl(200, 100%, 70%)', // bright cyan/blue accent
  secondary: 'hsl(280, 100%, 70%)', // purple for AI features
  success: 'hsl(142, 86%, 28%)', // vibrant green
  accent: 'hsl(190, 100%, 50%)', // cyan for data/analytics
  warning: 'hsl(45, 100%, 60%)',
  neutral: 'hsl(210, 15%, 20%)'
};

const CHART_COLORS = [
  COLORS.primary,
  COLORS.secondary,
  COLORS.success,
  COLORS.accent,
  COLORS.warning,
  '#8884d8',
  '#82ca9d',
  '#ffc658',
  '#ff7300',
  '#0088fe'
];

function getDateRange(range: TimeRange): { startDate: string; endDate: string } {
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(endDate.getDate() - timeRangeOptions[range].days);
  
  return {
    startDate: startDate.toISOString(),
    endDate: endDate.toISOString()
  };
}

function OverviewCard({ title, value, description, icon: Icon, trend }: {
  title: string;
  value: string | number;
  description: string;
  icon: any;
  trend?: 'up' | 'down' | 'neutral';
}) {
  return (
    <Card data-testid={`card-overview-${title.toLowerCase().replace(/\s+/g, '-')}`} className="hover-elevate">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold" data-testid={`text-value-${title.toLowerCase().replace(/\s+/g, '-')}`}>
          {value}
        </div>
        <p className="text-xs text-muted-foreground">
          {description}
          {trend && (
            <span className={`ml-2 inline-flex items-center ${
              trend === 'up' ? 'text-green-500' : 
              trend === 'down' ? 'text-red-500' : 'text-muted-foreground'
            }`}>
              {trend === 'up' && <TrendingUp className="h-3 w-3 mr-1" />}
              {trend === 'down' && <TrendingUp className="h-3 w-3 mr-1 rotate-180" />}
            </span>
          )}
        </p>
      </CardContent>
    </Card>
  );
}

function LoadingSkeleton() {
  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i}>
            <CardHeader className="pb-2">
              <Skeleton className="h-4 w-24" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-16 mb-2" />
              <Skeleton className="h-3 w-32" />
            </CardContent>
          </Card>
        ))}
      </div>
      
      <div className="grid gap-6 md:grid-cols-2">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i}>
            <CardHeader>
              <Skeleton className="h-5 w-32" />
              <Skeleton className="h-3 w-48" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-64 w-full" />
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}

export default function AnalyticsDashboard() {
  const [timeRange, setTimeRange] = useState<TimeRange>('1m');
  const dateRange = getDateRange(timeRange);
  const currentUserId = getCurrentUserId();

  // Analytics data queries
  const { data: overview, isLoading: overviewLoading } = useQuery<AnalyticsOverview>({
    queryKey: ['/api/analytics', currentUserId, 'overview', dateRange],
    queryFn: async () => {
      const params = new URLSearchParams({
        startDate: dateRange.startDate,
        endDate: dateRange.endDate
      });
      const response = await fetch(`/api/analytics/${currentUserId}/overview?${params}`);
      if (!response.ok) throw new Error('Failed to fetch overview');
      return response.json();
    }
  });

  const { data: trends, isLoading: trendsLoading } = useQuery<WorkoutTrend[]>({
    queryKey: ['/api/analytics', currentUserId, 'trends', dateRange],
    queryFn: async () => {
      const params = new URLSearchParams({
        startDate: dateRange.startDate,
        endDate: dateRange.endDate,
        groupBy: timeRange === '1w' ? 'day' : timeRange === '1y' ? 'month' : 'week'
      });
      const response = await fetch(`/api/analytics/${currentUserId}/trends?${params}`);
      if (!response.ok) throw new Error('Failed to fetch trends');
      return response.json();
    }
  });

  const { data: volumeProgression, isLoading: volumeLoading } = useQuery<VolumeProgression[]>({
    queryKey: ['/api/analytics', currentUserId, 'volume-progression', dateRange],
    queryFn: async () => {
      const params = new URLSearchParams({
        startDate: dateRange.startDate,
        endDate: dateRange.endDate
      });
      const response = await fetch(`/api/analytics/${currentUserId}/volume-progression?${params}`);
      if (!response.ok) throw new Error('Failed to fetch volume progression');
      return response.json();
    }
  });

  const { data: personalRecords, isLoading: recordsLoading } = useQuery<PersonalRecord[]>({
    queryKey: ['/api/analytics', currentUserId, 'personal-records'],
    queryFn: async () => {
      const params = new URLSearchParams({ limit: '10' });
      const response = await fetch(`/api/analytics/${currentUserId}/personal-records?${params}`);
      if (!response.ok) throw new Error('Failed to fetch personal records');
      return response.json();
    }
  });

  const { data: muscleGroups, isLoading: muscleLoading } = useQuery<MuscleGroupDistribution[]>({
    queryKey: ['/api/analytics', currentUserId, 'muscle-groups', dateRange],
    queryFn: async () => {
      const params = new URLSearchParams({
        startDate: dateRange.startDate,
        endDate: dateRange.endDate
      });
      const response = await fetch(`/api/analytics/${currentUserId}/muscle-groups?${params}`);
      if (!response.ok) throw new Error('Failed to fetch muscle groups');
      return response.json();
    }
  });

  const { data: goalProgress, isLoading: goalsLoading } = useQuery<GoalProgress[]>({
    queryKey: ['/api/analytics', currentUserId, 'goals'],
    queryFn: async () => {
      const response = await fetch(`/api/analytics/${currentUserId}/goals`);
      if (!response.ok) throw new Error('Failed to fetch goal progress');
      return response.json();
    }
  });

  const { data: achievements, isLoading: achievementsLoading } = useQuery<Achievement[]>({
    queryKey: ['/api/analytics', currentUserId, 'achievements'],
    queryFn: async () => {
      const params = new URLSearchParams({ limit: '5' });
      const response = await fetch(`/api/analytics/${currentUserId}/achievements?${params}`);
      if (!response.ok) throw new Error('Failed to fetch achievements');
      return response.json();
    }
  });

  // Nutrition analytics queries
  const { data: nutritionGoals, isLoading: nutritionGoalsLoading } = useQuery({
    queryKey: ['/api/nutrition/goals', currentUserId],
    queryFn: () => apiRequest(`/api/nutrition/goals/${currentUserId}`)
  });

  const { data: nutritionSummary, isLoading: nutritionSummaryLoading } = useQuery({
    queryKey: ['/api/nutrition/summary', currentUserId],
    queryFn: () => apiRequest(`/api/nutrition/summary?userId=${currentUserId}`)
  });

  const { data: weeklyNutrition, isLoading: weeklyNutritionLoading } = useQuery({
    queryKey: ['/api/nutrition/summary', currentUserId, 'weekly', dateRange],
    queryFn: () => {
      const params = new URLSearchParams({
        userId: currentUserId,
        startDate: dateRange.startDate,
        endDate: dateRange.endDate
      });
      return apiRequest(`/api/nutrition/summary?${params}`);
    }
  });

  const isLoading = overviewLoading || trendsLoading || volumeLoading || recordsLoading || muscleLoading || goalsLoading || achievementsLoading || nutritionGoalsLoading || nutritionSummaryLoading || weeklyNutritionLoading;

  if (isLoading) {
    return <LoadingSkeleton />;
  }

  // Process volume progression data for charts
  const volumeChartData = volumeProgression?.reduce((acc, item) => {
    const existingDate = acc.find(d => d.date === item.date);
    if (existingDate) {
      existingDate.totalVolume += item.volume;
    } else {
      acc.push({
        date: item.date,
        totalVolume: item.volume,
        oneRepMax: item.oneRepMax || 0
      });
    }
    return acc;
  }, [] as { date: string; totalVolume: number; oneRepMax: number }[]) || [];

  return (
    <div className="container mx-auto px-4 py-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-foreground" data-testid="heading-analytics-dashboard">
            Performance Analytics
          </h1>
          <p className="text-muted-foreground">
            Track your fitness journey and monitor your progress over time
          </p>
        </div>
        
        <Select value={timeRange} onValueChange={(value: TimeRange) => setTimeRange(value)}>
          <SelectTrigger className="w-48" data-testid="select-time-range">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {Object.entries(timeRangeOptions).map(([key, { label }]) => (
              <SelectItem key={key} value={key}>{label}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Overview Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-6">
        <OverviewCard
          title="Total Workouts"
          value={overview?.totalWorkouts || 0}
          description="Completed sessions"
          icon={Activity}
          trend="up"
        />
        <OverviewCard
          title="Current Streak"
          value={overview?.currentStreak || 0}
          description="Days in a row"
          icon={Flame}
          trend={overview?.currentStreak && overview.currentStreak > 0 ? "up" : "neutral"}
        />
        <OverviewCard
          title="Total Volume"
          value={`${Math.round((overview?.totalVolume || 0) / 1000)}k`}
          description="kg lifted"
          icon={BarChart3}
          trend="up"
        />
        <OverviewCard
          title="Personal Records"
          value={overview?.personalRecords || 0}
          description="New PRs achieved"
          icon={Trophy}
          trend={overview?.personalRecords && overview.personalRecords > 0 ? "up" : "neutral"}
        />
        <OverviewCard
          title="Today's Calories"
          value={Math.round(nutritionSummary?.totalCalories || 0)}
          description={`of ${nutritionGoals?.calorieTarget || 2000} target`}
          icon={Flame}
          trend={nutritionSummary?.totalCalories && nutritionGoals?.calorieTarget && nutritionSummary.totalCalories >= nutritionGoals.calorieTarget * 0.8 ? "up" : "neutral"}
        />
        <OverviewCard
          title="Protein Intake"
          value={`${Math.round(nutritionSummary?.totalProtein || 0)}g`}
          description={`of ${nutritionGoals?.proteinTarget || 150}g target`}
          icon={Utensils}
          trend={nutritionSummary?.totalProtein && nutritionGoals?.proteinTarget && nutritionSummary.totalProtein >= nutritionGoals.proteinTarget * 0.8 ? "up" : "neutral"}
        />
      </div>

      {/* Main Charts */}
      <Tabs defaultValue="trends" className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="trends" data-testid="tab-trends">
            <LineChartIcon className="h-4 w-4 mr-2" />
            Trends
          </TabsTrigger>
          <TabsTrigger value="volume" data-testid="tab-volume">
            <BarChart3 className="h-4 w-4 mr-2" />
            Volume
          </TabsTrigger>
          <TabsTrigger value="muscle-groups" data-testid="tab-muscle-groups">
            <PieChartIcon className="h-4 w-4 mr-2" />
            Muscle Groups
          </TabsTrigger>
          <TabsTrigger value="nutrition" data-testid="tab-nutrition">
            <Apple className="h-4 w-4 mr-2" />
            Nutrition
          </TabsTrigger>
          <TabsTrigger value="goals" data-testid="tab-goals">
            <Target className="h-4 w-4 mr-2" />
            Goals
          </TabsTrigger>
        </TabsList>

        <TabsContent value="trends" className="space-y-4">
          <div className="grid gap-6 md:grid-cols-2">
            {/* Workout Frequency Chart */}
            <Card data-testid="card-workout-frequency">
              <CardHeader>
                <CardTitle>Workout Frequency</CardTitle>
                <CardDescription>
                  Number of workouts over time
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={trends}>
                    <CartesianGrid strokeDasharray="3 3" stroke="hsl(210, 15%, 20%)" />
                    <XAxis 
                      dataKey="date" 
                      stroke="hsl(210, 10%, 40%)"
                      fontSize={12}
                    />
                    <YAxis stroke="hsl(210, 10%, 40%)" fontSize={12} />
                    <Tooltip 
                      contentStyle={{ 
                        backgroundColor: 'hsl(210, 40%, 8%)', 
                        border: '1px solid hsl(210, 15%, 20%)',
                        borderRadius: '8px',
                        color: 'hsl(210, 40%, 98%)'
                      }}
                    />
                    <Area
                      type="monotone"
                      dataKey="workouts"
                      stroke={COLORS.primary}
                      fill={COLORS.primary}
                      fillOpacity={0.3}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Duration Chart */}
            <Card data-testid="card-duration-trends">
              <CardHeader>
                <CardTitle>Workout Duration</CardTitle>
                <CardDescription>
                  Average session length in minutes
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={trends}>
                    <CartesianGrid strokeDasharray="3 3" stroke="hsl(210, 15%, 20%)" />
                    <XAxis 
                      dataKey="date" 
                      stroke="hsl(210, 10%, 40%)"
                      fontSize={12}
                    />
                    <YAxis stroke="hsl(210, 10%, 40%)" fontSize={12} />
                    <Tooltip 
                      contentStyle={{ 
                        backgroundColor: 'hsl(210, 40%, 8%)', 
                        border: '1px solid hsl(210, 15%, 20%)',
                        borderRadius: '8px',
                        color: 'hsl(210, 40%, 98%)'
                      }}
                    />
                    <Line
                      type="monotone"
                      dataKey="duration"
                      stroke={COLORS.success}
                      strokeWidth={2}
                      dot={{ fill: COLORS.success, strokeWidth: 2, r: 4 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="volume" className="space-y-4">
          <div className="grid gap-6">
            <Card data-testid="card-volume-progression">
              <CardHeader>
                <CardTitle>Volume Progression</CardTitle>
                <CardDescription>
                  Total volume lifted over time
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={400}>
                  <AreaChart data={volumeChartData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="hsl(210, 15%, 20%)" />
                    <XAxis 
                      dataKey="date" 
                      stroke="hsl(210, 10%, 40%)"
                      fontSize={12}
                    />
                    <YAxis stroke="hsl(210, 10%, 40%)" fontSize={12} />
                    <Tooltip 
                      contentStyle={{ 
                        backgroundColor: 'hsl(210, 40%, 8%)', 
                        border: '1px solid hsl(210, 15%, 20%)',
                        borderRadius: '8px',
                        color: 'hsl(210, 40%, 98%)'
                      }}
                      formatter={(value, name) => [
                        `${value}${name === 'totalVolume' ? 'kg' : 'kg'}`,
                        name === 'totalVolume' ? 'Volume' : '1RM'
                      ]}
                    />
                    <Area
                      type="monotone"
                      dataKey="totalVolume"
                      stroke={COLORS.accent}
                      fill={COLORS.accent}
                      fillOpacity={0.3}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="muscle-groups" className="space-y-4">
          <div className="grid gap-6 md:grid-cols-2">
            <Card data-testid="card-muscle-group-chart">
              <CardHeader>
                <CardTitle>Muscle Group Distribution</CardTitle>
                <CardDescription>
                  Workout focus by muscle groups
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={muscleGroups}
                      cx="50%"
                      cy="50%"
                      outerRadius={100}
                      fill="#8884d8"
                      dataKey="percentage"
                      label={({ muscleGroup, percentage }) => `${muscleGroup} ${percentage}%`}
                    >
                      {muscleGroups?.map((_, index) => (
                        <Cell key={`cell-${index}`} fill={CHART_COLORS[index % CHART_COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip 
                      contentStyle={{ 
                        backgroundColor: 'hsl(210, 40%, 8%)', 
                        border: '1px solid hsl(210, 15%, 20%)',
                        borderRadius: '8px',
                        color: 'hsl(210, 40%, 98%)'
                      }}
                      formatter={(value) => [`${value}%`, 'Percentage']}
                    />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card data-testid="card-muscle-group-list">
              <CardHeader>
                <CardTitle>Training Balance</CardTitle>
                <CardDescription>
                  Sessions per muscle group
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {muscleGroups?.slice(0, 6).map((group, index) => (
                    <div key={group.muscleGroup} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div 
                          className="w-3 h-3 rounded-full" 
                          style={{ backgroundColor: CHART_COLORS[index % CHART_COLORS.length] }}
                        />
                        <span className="text-sm font-medium capitalize">
                          {group.muscleGroup}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-muted-foreground">
                          {group.sessions} sessions
                        </span>
                        <Badge variant="outline">{group.percentage}%</Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="nutrition" className="space-y-4">
          <div className="grid gap-6 md:grid-cols-2">
            {/* Daily Calorie Progress */}
            <Card data-testid="card-calorie-progress">
              <CardHeader>
                <CardTitle>Daily Calorie Progress</CardTitle>
                <CardDescription>
                  Today's calorie intake vs your goal
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Calories</span>
                    <span className="text-sm text-muted-foreground">
                      {Math.round(nutritionSummary?.totalCalories || 0)} / {nutritionGoals?.calorieTarget || 2000}
                    </span>
                  </div>
                  <Progress 
                    value={((nutritionSummary?.totalCalories || 0) / (nutritionGoals?.calorieTarget || 2000)) * 100} 
                    className="h-3"
                  />
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div>
                      <div className="text-lg font-semibold text-primary">
                        {Math.round(nutritionSummary?.totalProtein || 0)}g
                      </div>
                      <div className="text-xs text-muted-foreground">Protein</div>
                    </div>
                    <div>
                      <div className="text-lg font-semibold text-success">
                        {Math.round(nutritionSummary?.totalCarbs || 0)}g
                      </div>
                      <div className="text-xs text-muted-foreground">Carbs</div>
                    </div>
                    <div>
                      <div className="text-lg font-semibold text-warning">
                        {Math.round(nutritionSummary?.totalFat || 0)}g
                      </div>
                      <div className="text-xs text-muted-foreground">Fat</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Macro Breakdown Chart */}
            <Card data-testid="card-macro-breakdown">
              <CardHeader>
                <CardTitle>Macro Breakdown</CardTitle>
                <CardDescription>
                  Today's macronutrient distribution
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={250}>
                  <PieChart>
                    <Pie
                      data={[
                        { 
                          name: 'Protein', 
                          value: (nutritionSummary?.totalProtein || 0) * 4, 
                          fill: COLORS.primary 
                        },
                        { 
                          name: 'Carbs', 
                          value: (nutritionSummary?.totalCarbs || 0) * 4, 
                          fill: COLORS.success 
                        },
                        { 
                          name: 'Fat', 
                          value: (nutritionSummary?.totalFat || 0) * 9, 
                          fill: COLORS.warning 
                        }
                      ]}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={100}
                      paddingAngle={5}
                      dataKey="value"
                    >
                      {[0, 1, 2].map((index) => (
                        <Cell key={`cell-${index}`} />
                      ))}
                    </Pie>
                    <Tooltip 
                      contentStyle={{ 
                        backgroundColor: 'hsl(210, 40%, 8%)', 
                        border: '1px solid hsl(210, 15%, 20%)',
                        borderRadius: '8px',
                        color: 'hsl(210, 40%, 98%)'
                      }}
                      formatter={(value: number, name: string) => [
                        `${Math.round(value)} cal`,
                        name
                      ]}
                    />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Weekly Nutrition Trends */}
            <Card className="md:col-span-2" data-testid="card-nutrition-trends">
              <CardHeader>
                <CardTitle>Weekly Nutrition Trends</CardTitle>
                <CardDescription>
                  Your nutrition progress over the selected time period
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={Array.isArray(weeklyNutrition) ? weeklyNutrition : []}>
                    <CartesianGrid strokeDasharray="3 3" stroke="hsl(210, 15%, 20%)" />
                    <XAxis 
                      dataKey="date" 
                      stroke="hsl(210, 10%, 40%)"
                      fontSize={12}
                    />
                    <YAxis stroke="hsl(210, 10%, 40%)" fontSize={12} />
                    <Tooltip 
                      contentStyle={{ 
                        backgroundColor: 'hsl(210, 40%, 8%)', 
                        border: '1px solid hsl(210, 15%, 20%)',
                        borderRadius: '8px',
                        color: 'hsl(210, 40%, 98%)'
                      }}
                    />
                    <Line
                      type="monotone"
                      dataKey="totalCalories"
                      stroke={COLORS.primary}
                      strokeWidth={2}
                      dot={{ fill: COLORS.primary, strokeWidth: 2, r: 4 }}
                      name="Calories"
                    />
                    <Line
                      type="monotone"
                      dataKey="totalProtein"
                      stroke={COLORS.success}
                      strokeWidth={2}
                      dot={{ fill: COLORS.success, strokeWidth: 2, r: 4 }}
                      name="Protein (g)"
                    />
                    <Line
                      type="monotone"
                      dataKey="totalCarbs"
                      stroke={COLORS.accent}
                      strokeWidth={2}
                      dot={{ fill: COLORS.accent, strokeWidth: 2, r: 4 }}
                      name="Carbs (g)"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Nutrition Goals Progress */}
            <Card data-testid="card-nutrition-goals">
              <CardHeader>
                <CardTitle>Nutrition Goals Progress</CardTitle>
                <CardDescription>
                  Track your daily nutrition targets
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Protein</span>
                      <span className="text-sm text-muted-foreground">
                        {Math.round(nutritionSummary?.totalProtein || 0)}g / {nutritionGoals?.proteinTarget || 150}g
                      </span>
                    </div>
                    <Progress 
                      value={((nutritionSummary?.totalProtein || 0) / (nutritionGoals?.proteinTarget || 150)) * 100} 
                      className="h-2"
                    />
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Carbohydrates</span>
                      <span className="text-sm text-muted-foreground">
                        {Math.round(nutritionSummary?.totalCarbs || 0)}g / {nutritionGoals?.carbTarget || 250}g
                      </span>
                    </div>
                    <Progress 
                      value={((nutritionSummary?.totalCarbs || 0) / (nutritionGoals?.carbTarget || 250)) * 100} 
                      className="h-2"
                    />
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Fat</span>
                      <span className="text-sm text-muted-foreground">
                        {Math.round(nutritionSummary?.totalFat || 0)}g / {nutritionGoals?.fatTarget || 65}g
                      </span>
                    </div>
                    <Progress 
                      value={((nutritionSummary?.totalFat || 0) / (nutritionGoals?.fatTarget || 65)) * 100} 
                      className="h-2"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Diet Information */}
            <Card data-testid="card-diet-info">
              <CardHeader>
                <CardTitle>Diet Profile</CardTitle>
                <CardDescription>
                  Your nutrition preferences and restrictions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div>
                    <span className="text-sm font-medium">Diet Type: </span>
                    <Badge variant="outline" className="capitalize">
                      {nutritionGoals?.dietType?.replace('_', ' ') || 'General'}
                    </Badge>
                  </div>
                  {nutritionGoals?.restrictions && nutritionGoals.restrictions.length > 0 && (
                    <div>
                      <span className="text-sm font-medium block mb-2">Dietary Restrictions:</span>
                      <div className="flex flex-wrap gap-1">
                        {nutritionGoals.restrictions.map((restriction, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {restriction}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                  {nutritionGoals?.preferredCuisines && nutritionGoals.preferredCuisines.length > 0 && (
                    <div>
                      <span className="text-sm font-medium block mb-2">Preferred Cuisines:</span>
                      <div className="flex flex-wrap gap-1">
                        {nutritionGoals.preferredCuisines.map((cuisine, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {cuisine}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="goals" className="space-y-4">
          <div className="grid gap-6 md:grid-cols-2">
            <Card data-testid="card-goal-progress">
              <CardHeader>
                <CardTitle>Goal Progress</CardTitle>
                <CardDescription>
                  Track your fitness objectives
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {goalProgress?.map((goal) => (
                    <div key={goal.goalType} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium capitalize">
                          {goal.goalType.replace('_', ' ')}
                        </span>
                        <Badge variant={goal.onTrack ? "default" : "secondary"}>
                          {goal.percentage}%
                        </Badge>
                      </div>
                      <Progress 
                        value={goal.percentage} 
                        className="h-2"
                        data-testid={`progress-${goal.goalType}`}
                      />
                      <p className="text-xs text-muted-foreground">
                        {goal.current} / {goal.target} {goal.onTrack ? '(On Track)' : '(Needs Focus)'}
                      </p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card data-testid="card-achievements">
              <CardHeader>
                <CardTitle>Recent Achievements</CardTitle>
                <CardDescription>
                  Your latest milestones and records
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {achievements?.map((achievement) => (
                    <div key={achievement.id} className="flex items-start gap-3 p-2 rounded-lg bg-muted/50">
                      <div className="p-1 rounded-full bg-primary/10">
                        <Award className="h-4 w-4 text-primary" />
                      </div>
                      <div className="flex-1">
                        <h4 className="text-sm font-medium">{achievement.title}</h4>
                        <p className="text-xs text-muted-foreground">
                          {achievement.description}
                        </p>
                        <Badge variant="outline" className="mt-1 text-xs">
                          {achievement.type}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Personal Records */}
      <Card data-testid="card-personal-records">
        <CardHeader>
          <CardTitle>Personal Records</CardTitle>
          <CardDescription>
            Your best performances and recent improvements
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {personalRecords?.slice(0, 6).map((pr) => (
              <div 
                key={pr.id}
                className="p-4 rounded-lg border bg-muted/30 hover-elevate"
                data-testid={`pr-card-${pr.id}`}
              >
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-sm">{pr.exercise}</h4>
                  <Trophy className="h-4 w-4 text-yellow-500" />
                </div>
                <div className="text-2xl font-bold">
                  {pr.weight}kg × {pr.reps}
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  Achieved {new Date(pr.achievedAt).toLocaleDateString()}
                </p>
                {pr.previousBest && (
                  <p className="text-xs text-green-500 mt-1">
                    +{pr.weight - pr.previousBest.weight}kg improvement
                  </p>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Additional Stats */}
      <div className="grid gap-6 md:grid-cols-3">
        <Card data-testid="card-consistency">
          <CardHeader>
            <CardTitle>Consistency Score</CardTitle>
            <CardDescription>Based on your workout frequency goals</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center space-y-2">
              <div className="text-3xl font-bold text-primary">
                {overview?.workoutConsistency || 0}%
              </div>
              <Progress value={overview?.workoutConsistency || 0} className="h-2" />
              <p className="text-sm text-muted-foreground">
                {overview?.workoutConsistency && overview.workoutConsistency >= 80 
                  ? "Excellent consistency!" 
                  : "Room for improvement"}
              </p>
            </div>
          </CardContent>
        </Card>

        <Card data-testid="card-favorite-exercise">
          <CardHeader>
            <CardTitle>Favorite Exercise</CardTitle>
            <CardDescription>Your most performed exercise</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center space-y-2">
              <div className="text-lg font-semibold">
                {overview?.favoriteExercise || "No data"}
              </div>
              <p className="text-sm text-muted-foreground">
                Most frequently performed
              </p>
            </div>
          </CardContent>
        </Card>

        <Card data-testid="card-avg-duration">
          <CardHeader>
            <CardTitle>Average Duration</CardTitle>
            <CardDescription>Mean workout session length</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center space-y-2">
              <div className="text-3xl font-bold">
                {Math.round(overview?.averageWorkoutDuration || 0)}
                <span className="text-sm font-normal text-muted-foreground ml-1">min</span>
              </div>
              <p className="text-sm text-muted-foreground">
                Per workout session
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}