import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, MessageCircle, X } from "lucide-react";
import { useState } from "react";
import { <PERSON> } from "wouter";
import ThemeToggle from "./ThemeToggle";
import AIChatCoach from "./AIChatCoach";
import circuitLogo from "@assets/ChatGPT Image Sep 14, 2025, 03_07_53 PM_1758219151827.png";

export default function Header() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [globalChatOpen, setGlobalChatOpen] = useState(false);

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center justify-between px-4 lg:px-8">
        {/* Logo */}
        <div className="flex items-center space-x-3">
          <img src={circuitLogo} alt="Circuit" className="h-8 w-8" />
          <div className="flex flex-col">
            <span className="text-xl font-bold text-foreground">CIRCUIT</span>
            <span className="text-xs text-muted-foreground">Coach-led • AI-assisted</span>
          </div>
        </div>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center space-x-8">
          <Link 
            href="/" 
            className="text-sm font-medium text-foreground hover:text-primary transition-colors"
            data-testid="link-home"
          >
            Home
          </Link>
          <Link 
            href="/workout" 
            className="text-sm font-medium text-muted-foreground hover:text-primary transition-colors flex items-center gap-2"
            data-testid="link-workout"
          >
            <Dumbbell className="h-4 w-4" />
            Workout
          </Link>
          <Link 
            href="/profile" 
            className="text-sm font-medium text-muted-foreground hover:text-primary transition-colors flex items-center gap-2"
            data-testid="link-profile"
          >
            <User className="h-4 w-4" />
            Profile
          </Link>
          <Link 
            href="/analytics" 
            className="text-sm font-medium text-muted-foreground hover:text-primary transition-colors flex items-center gap-2"
            data-testid="link-analytics"
          >
            <TrendingUp className="h-4 w-4" />
            Analytics
          </Link>
          <a 
            href="#ai-tools" 
            className="text-sm font-medium text-muted-foreground hover:text-primary transition-colors flex items-center gap-2"
            data-testid="link-ai-tools"
          >
            <Brain className="h-4 w-4" />
            AI Tools
          </a>
          <a 
            href="#form-analysis" 
            className="text-sm font-medium text-muted-foreground hover:text-primary transition-colors flex items-center gap-2"
            data-testid="link-form-analysis"
          >
            <BarChart3 className="h-4 w-4" />
            Form Analysis
          </a>
          <a 
            href="#plans" 
            className="text-sm font-medium text-muted-foreground hover:text-primary transition-colors flex items-center gap-2"
            data-testid="link-plans"
          >
            <Target className="h-4 w-4" />
            Plans
          </a>
        </nav>

        {/* CTA Buttons */}
        <div className="hidden md:flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setGlobalChatOpen(true)}
            className="flex items-center gap-2 text-primary hover:text-primary"
            data-testid="button-global-chat"
          >
            <MessageCircle className="h-4 w-4" />
            AI Coach
          </Button>
          <ThemeToggle />
          <Button variant="outline" data-testid="button-login">
            Sign In
          </Button>
          <Button data-testid="button-get-started">
            Get Started
          </Button>
        </div>

        {/* Mobile Menu Button */}
        <Button 
          variant="ghost" 
          size="icon" 
          className="md:hidden"
          onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          data-testid="button-mobile-menu"
        >
          <Menu className="h-5 w-5" />
        </Button>
      </div>

      {/* Mobile Menu */}
      {mobileMenuOpen && (
        <div className="md:hidden border-t bg-background">
          <div className="container px-4 py-4 space-y-4">
            <Link href="/" className="block text-sm font-medium">Home</Link>
            <Link href="/workout" className="block text-sm font-medium">Workout</Link>
            <Link href="/profile" className="block text-sm font-medium">Profile</Link>
            <Link href="/analytics" className="block text-sm font-medium">Analytics</Link>
            <a href="#ai-tools" className="block text-sm font-medium">AI Tools</a>
            <a href="#progress" className="block text-sm font-medium">Progress</a>
            <a href="#plans" className="block text-sm font-medium">Plans</a>
            <Button
              variant="ghost"
              className="w-full justify-start text-sm font-medium"
              onClick={() => {
                setGlobalChatOpen(true);
                setMobileMenuOpen(false);
              }}
              data-testid="button-mobile-chat"
            >
              <MessageCircle className="h-4 w-4 mr-2" />
              AI Coach
            </Button>
            <div className="pt-4 space-y-2">
              <Button variant="outline" className="w-full">Sign In</Button>
              <Button className="w-full">Get Started</Button>
            </div>
          </div>
        </div>
      )}

      {/* Global AI Chat Modal */}
      {globalChatOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50">
          <div className="bg-background rounded-lg shadow-lg max-w-2xl w-full h-[600px] max-h-[90vh] flex flex-col">
            <div className="flex items-center justify-between p-6 border-b">
              <div className="flex items-center gap-2">
                <MessageCircle className="h-5 w-5 text-primary" />
                <h2 className="text-lg font-semibold">AI Fitness Coach</h2>
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setGlobalChatOpen(false)}
                data-testid="button-close-global-chat"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex-1 overflow-hidden">
              <AIChatCoach className="h-full" data-testid="global-chat" />
            </div>
          </div>
        </div>
      )}
    </header>
  );
}