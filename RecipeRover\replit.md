# Fit Circuit - AI-Powered Fitness Coaching App

## Overview

Fit Circuit is a comprehensive fitness coaching web application that combines human expertise with AI technology. The app provides personalized workout tracking, nutrition planning, progress monitoring, and real-time form analysis through computer vision. Built as a modern full-stack application, it offers AI-powered coaching tools including chat assistance, meal planning, workout generation, and progress analysis with visual feedback capabilities.

## User Preferences

Preferred communication style: Simple, everyday language.

## System Architecture

### Frontend Architecture
- **Framework**: React 18+ with TypeScript and Vite for build tooling
- **Routing**: Wouter for lightweight client-side routing
- **UI Components**: shadcn/ui component library built on Radix UI primitives
- **Styling**: Tailwind CSS v4 with custom design tokens and dark/light theme support
- **State Management**: React Query (TanStack Query) for server state and caching
- **Forms**: React Hook Form with Zod validation schemas
- **Charts**: Recharts for data visualization and analytics

### Backend Architecture
- **Runtime**: Node.js with Express.js server framework
- **Language**: TypeScript with ES modules
- **Database**: PostgreSQL with Drizzle ORM for type-safe database operations
- **Storage**: File-based JSON storage for development with migration path to full database
- **Session Management**: Session-based authentication (planned) with connect-pg-simple

### Data Layer Design
- **ORM**: Drizzle ORM with schema-first approach and automatic type generation
- **Database**: Neon PostgreSQL serverless database with connection pooling
- **Schema**: Comprehensive fitness data models including users, workouts, exercises, meals, nutrition tracking, and analytics
- **Validation**: Zod schemas for runtime type checking and API validation

### AI Integration Architecture
- **Chat System**: OpenAI integration for conversational AI coaching with context awareness
- **Computer Vision**: OpenAI Vision API for exercise form analysis and progress photo comparison
- **Content Generation**: AI-powered meal plan and workout plan generation with structured JSON outputs
- **Real-time Features**: Rate limiting and streaming capabilities for AI interactions

### Authentication & Security
- **Current State**: Mock user system for development with consistent user ID management
- **Planned**: Supabase Auth integration with JWT tokens and role-based access control
- **Security**: API key management for external services, rate limiting for AI endpoints

## External Dependencies

### Core Infrastructure
- **Database**: Neon PostgreSQL serverless database for production data storage
- **Build Tools**: Vite for frontend bundling and development server
- **Package Manager**: npm with lock file for consistent dependency management

### AI & Machine Learning Services
- **OpenAI API**: GPT models for chat, content generation, and vision analysis
- **Perplexity API**: Alternative AI provider for specialized coaching responses

### UI & Design Framework
- **Radix UI**: Headless component primitives for accessibility and keyboard navigation
- **Tailwind CSS**: Utility-first styling with custom design system
- **Lucide Icons**: Icon library for consistent visual elements
- **Google Fonts**: Inter and JetBrains Mono for typography

### Development & Deployment
- **TypeScript**: Static typing across frontend and backend
- **ESBuild**: Fast JavaScript bundler for production builds
- **Replit**: Development environment with live preview capabilities

### Data & Analytics
- **Recharts**: Chart library for fitness analytics and progress visualization
- **Date-fns**: Date manipulation for workout and meal scheduling
- **React Query**: Server state management with caching and synchronization