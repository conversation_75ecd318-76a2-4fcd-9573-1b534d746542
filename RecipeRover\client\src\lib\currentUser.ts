// Shared current user mechanism for the entire application
// This provides a consistent way to get the current user ID across all components

// For now, we use a mock user ID since there's no authentication system
// This can be easily replaced with real authentication when implemented
export const CURRENT_USER_ID = "user-123";

// Function to get current user ID - makes it easy to replace with real auth later
export const getCurrentUserId = (): string => {
  return CURRENT_USER_ID;
};

// This can be extended to support user context, authentication, etc.
export interface CurrentUser {
  id: string;
  // Can be extended with other user properties when needed
}

export const getCurrentUser = (): CurrentUser => {
  return {
    id: getCurrentUserId(),
  };
};