import { 
  type User, type InsertUser, 
  type UserProfile, type InsertUserProfile, type UpdateUserProfile,
  type Exercise, type InsertExercise,
  type WorkoutSession, type InsertWorkoutSession,
  type SessionExercise, type InsertSessionExercise,
  type ExerciseSet, type InsertExerciseSet,
  type ChatSession, type InsertChatSession, type UpdateChatSession,
  type ChatMessage, type InsertChatMessage,
  type DateRange, type AnalyticsOverview, type WorkoutTrend, type VolumeProgression,
  type PersonalRecord, type MuscleGroupDistribution, type GoalProgress, type Achievement,
  type Food, type InsertFood, type FoodPortion, type InsertFoodPortion,
  type Meal, type InsertMeal, type UpdateMeal, type MealItem, type InsertMealItem, type UpdateMealItem,
  type DailyNutritionSummary, type MealPlan, type InsertMealPlan, type MealPlanItem, type InsertMealPlanItem
} from "@shared/schema";
import { randomUUID } from "crypto";

// Helper function to calculate profile completion percentage
function calculateProfileCompletion(profile: any): number {
  const requiredFields = ['age', 'height', 'weight', 'fitnessLevel', 'goals', 'activityLevel'];
  const optionalFields = ['targetWeight', 'targetDate', 'preferredWorkoutDuration', 'weeklyWorkoutFrequency'];
  
  let completedRequired = 0;
  let completedOptional = 0;
  
  // Check required fields
  requiredFields.forEach(field => {
    if (profile[field] !== null && profile[field] !== undefined && 
        (Array.isArray(profile[field]) ? profile[field].length > 0 : true)) {
      completedRequired++;
    }
  });
  
  // Check optional fields
  optionalFields.forEach(field => {
    if (profile[field] !== null && profile[field] !== undefined &&
        (Array.isArray(profile[field]) ? profile[field].length > 0 : true)) {
      completedOptional++;
    }
  });
  
  // Weight required fields more heavily (60%) vs optional (40%)
  const requiredWeight = 0.6;
  const optionalWeight = 0.4;
  
  const requiredPercentage = (completedRequired / requiredFields.length) * requiredWeight;
  const optionalPercentage = (completedOptional / optionalFields.length) * optionalWeight;
  
  return Math.round((requiredPercentage + optionalPercentage) * 100);
}

// modify the interface with any CRUD methods
// you might need

// Analytics types now imported from shared schema

export interface IStorage {
  // User operations
  getUser(id: string): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  
  // User profile operations
  getUserProfile(userId: string): Promise<UserProfile | undefined>;
  createUserProfile(profile: InsertUserProfile): Promise<UserProfile>;
  updateUserProfile(userId: string, updates: UpdateUserProfile): Promise<UserProfile | undefined>;
  deleteUserProfile(userId: string): Promise<boolean>;
  
  // Exercise operations
  getAllExercises(): Promise<Exercise[]>;
  getExercise(id: string): Promise<Exercise | undefined>;
  createExercise(exercise: InsertExercise): Promise<Exercise>;
  searchExercises(query: string): Promise<Exercise[]>;
  filterExercises(filters: {
    difficulty?: string;
    equipment?: string[];
    muscleGroup?: string;
    category?: string;
  }): Promise<Exercise[]>;

  // Workout session operations
  createWorkoutSession(session: InsertWorkoutSession): Promise<WorkoutSession>;
  getWorkoutSession(id: string): Promise<WorkoutSession | undefined>;
  updateWorkoutSession(id: string, updates: Partial<WorkoutSession>): Promise<WorkoutSession | undefined>;
  getUserWorkoutSessions(userId: string): Promise<WorkoutSession[]>;
  getActiveWorkoutSession(userId: string): Promise<WorkoutSession | undefined>;

  // Session exercise operations
  addExerciseToSession(sessionExercise: InsertSessionExercise): Promise<SessionExercise>;
  getSessionExercises(sessionId: string): Promise<SessionExercise[]>;
  updateSessionExercise(id: string, updates: Partial<SessionExercise>): Promise<SessionExercise | undefined>;
  deleteSessionExercise(id: string): Promise<boolean>;

  // Exercise set operations
  addExerciseSet(exerciseSet: InsertExerciseSet): Promise<ExerciseSet>;
  getExerciseSets(sessionExerciseId: string): Promise<ExerciseSet[]>;
  updateExerciseSet(id: string, updates: Partial<ExerciseSet>): Promise<ExerciseSet | undefined>;
  deleteExerciseSet(id: string): Promise<boolean>;

  // Analytics operations
  getAnalyticsOverview(userId: string, dateRange?: DateRange): Promise<AnalyticsOverview>;
  getWorkoutTrends(userId: string, dateRange: DateRange, groupBy: 'day' | 'week' | 'month'): Promise<WorkoutTrend[]>;
  getVolumeProgression(userId: string, exercise?: string, dateRange?: DateRange): Promise<VolumeProgression[]>;
  getPersonalRecords(userId: string, exercise?: string, limit?: number): Promise<PersonalRecord[]>;
  getMuscleGroupDistribution(userId: string, dateRange?: DateRange): Promise<MuscleGroupDistribution[]>;
  getGoalProgress(userId: string): Promise<GoalProgress[]>;
  getAchievements(userId: string, limit?: number): Promise<Achievement[]>;

  // Chat operations
  createChatSession(session: InsertChatSession): Promise<ChatSession>;
  getChatSession(id: string): Promise<ChatSession | undefined>;
  updateChatSession(id: string, updates: UpdateChatSession): Promise<ChatSession | undefined>;
  getUserChatSessions(userId: string): Promise<ChatSession[]>;
  getActiveChatSession(userId: string): Promise<ChatSession | undefined>;
  deactivateOtherSessions(userId: string, activeSessionId: string): Promise<void>;
  deleteChatSession(sessionId: string): Promise<boolean>;
  addChatMessage(message: InsertChatMessage): Promise<ChatMessage>;
  getChatMessages(sessionId: string, limit?: number): Promise<ChatMessage[]>;
  deleteChatMessage(messageId: string): Promise<boolean>;
  getChatSessionWithMessages(sessionId: string, messageLimit?: number): Promise<(ChatSession & { messages: ChatMessage[] }) | undefined>;
  
  // Cleanup and retention policies
  cleanupOldSessions(retentionDays?: number): Promise<number>;
  cleanupOldMessages(retentionDays?: number): Promise<number>;
  getSessionStats(userId?: string): Promise<{ totalSessions: number; activeSessions: number; totalMessages: number; oldestSession?: Date; }>;

  // Nutrition - Food operations
  getAllFoods(): Promise<Food[]>;
  getFood(id: string): Promise<Food | undefined>;
  createFood(food: InsertFood): Promise<Food>;
  searchFoods(query: string): Promise<Food[]>;
  getUserCreatedFoods(userId: string): Promise<Food[]>;
  updateFood(id: string, updates: Partial<Food>): Promise<Food | undefined>;
  deleteFood(id: string): Promise<boolean>;

  // Food portion operations
  getFoodPortions(foodId: string): Promise<FoodPortion[]>;
  createFoodPortion(portion: InsertFoodPortion): Promise<FoodPortion>;
  deleteFoodPortion(id: string): Promise<boolean>;

  // Meal operations
  getMealsByDate(userId: string, date: string): Promise<Meal[]>;
  getMeal(id: string): Promise<Meal | undefined>;
  createMeal(meal: InsertMeal): Promise<Meal>;
  updateMeal(id: string, updates: UpdateMeal): Promise<Meal | undefined>;
  deleteMeal(id: string): Promise<boolean>;

  // Meal item operations
  getMealItems(mealId: string): Promise<MealItem[]>;
  addMealItem(mealItem: InsertMealItem): Promise<MealItem>;
  updateMealItem(id: string, updates: UpdateMealItem): Promise<MealItem | undefined>;
  deleteMealItem(id: string): Promise<boolean>;

  // Daily nutrition summary operations
  getDailyNutritionSummary(userId: string, date: string): Promise<DailyNutritionSummary | undefined>;
  updateDailyNutritionSummary(userId: string, date: string): Promise<DailyNutritionSummary>;
  getNutritionSummaryRange(userId: string, startDate: string, endDate: string): Promise<DailyNutritionSummary[]>;

  // Meal plan operations
  getUserMealPlans(userId: string): Promise<MealPlan[]>;
  getMealPlan(id: string): Promise<MealPlan | undefined>;
  createMealPlan(plan: InsertMealPlan): Promise<MealPlan>;
  deleteMealPlan(id: string): Promise<boolean>;

  // Meal plan item operations
  getMealPlanItems(mealPlanId: string): Promise<MealPlanItem[]>;
  addMealPlanItem(item: InsertMealPlanItem): Promise<MealPlanItem>;
  deleteMealPlanItem(id: string): Promise<boolean>;
  applyMealPlanToDate(mealPlanId: string, userId: string, date: string): Promise<Meal[]>;
}

export class MemStorage implements IStorage {
  private users: Map<string, User>;
  private userProfiles: Map<string, UserProfile>;
  private exercises: Map<string, Exercise>;
  private workoutSessions: Map<string, WorkoutSession>;
  private sessionExercises: Map<string, SessionExercise>;
  private exerciseSets: Map<string, ExerciseSet>;
  private chatSessions: Map<string, ChatSession>;
  private chatMessages: Map<string, ChatMessage>;
  // Nutrition storage
  private foods: Map<string, Food>;
  private foodPortions: Map<string, FoodPortion>;
  private meals: Map<string, Meal>;
  private mealItems: Map<string, MealItem>;
  private dailyNutritionSummaries: Map<string, DailyNutritionSummary>;
  private mealPlans: Map<string, MealPlan>;
  private mealPlanItems: Map<string, MealPlanItem>;

  constructor() {
    this.users = new Map();
    this.userProfiles = new Map();
    this.exercises = new Map();
    this.workoutSessions = new Map();
    this.sessionExercises = new Map();
    this.exerciseSets = new Map();
    this.chatSessions = new Map();
    this.chatMessages = new Map();
    // Initialize nutrition storage
    this.foods = new Map();
    this.foodPortions = new Map();
    this.meals = new Map();
    this.mealItems = new Map();
    this.dailyNutritionSummaries = new Map();
    this.mealPlans = new Map();
    this.mealPlanItems = new Map();
    
    // Seed some basic food data and exercises
    this.seedFoods();
    this.seedExercises();
  }

  async getUser(id: string): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.username === username,
    );
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = randomUUID();
    const user: User = { ...insertUser, id };
    this.users.set(id, user);
    return user;
  }

  // User profile operations
  async getUserProfile(userId: string): Promise<UserProfile | undefined> {
    return Array.from(this.userProfiles.values()).find(
      (profile) => profile.userId === userId,
    );
  }

  async createUserProfile(insertProfile: InsertUserProfile): Promise<UserProfile> {
    const id = randomUUID();
    const now = new Date();
    const profile: UserProfile = { 
      ...insertProfile, 
      id,
      createdAt: now,
      updatedAt: now,
      // Convert numeric values if provided
      height: insertProfile.height ? Number(insertProfile.height) : null,
      weight: insertProfile.weight ? Number(insertProfile.weight) : null,
      targetWeight: insertProfile.targetWeight ? Number(insertProfile.targetWeight) : null,
      isComplete: false, // Will be calculated below
    };
    
    // Calculate completion and set isComplete flag
    const completionPercentage = calculateProfileCompletion(profile);
    profile.isComplete = completionPercentage >= 80;
    
    this.userProfiles.set(id, profile);
    return profile;
  }

  async updateUserProfile(userId: string, updates: UpdateUserProfile): Promise<UserProfile | undefined> {
    const profile = Array.from(this.userProfiles.values()).find(p => p.userId === userId);
    if (!profile) return undefined;
    
    const updatedProfile = { 
      ...profile, 
      ...updates,
      updatedAt: new Date(),
      // Convert numeric values if provided
      height: updates.height !== undefined ? (updates.height ? Number(updates.height) : null) : profile.height,
      weight: updates.weight !== undefined ? (updates.weight ? Number(updates.weight) : null) : profile.weight,
      targetWeight: updates.targetWeight !== undefined ? (updates.targetWeight ? Number(updates.targetWeight) : null) : profile.targetWeight,
    };
    
    // Calculate completion and set isComplete flag
    const completionPercentage = calculateProfileCompletion(updatedProfile);
    updatedProfile.isComplete = completionPercentage >= 80;
    
    this.userProfiles.set(profile.id, updatedProfile);
    return updatedProfile;
  }

  async deleteUserProfile(userId: string): Promise<boolean> {
    const profile = Array.from(this.userProfiles.values()).find(p => p.userId === userId);
    if (!profile) return false;
    return this.userProfiles.delete(profile.id);
  }

  // Exercise operations
  async getAllExercises(): Promise<Exercise[]> {
    return Array.from(this.exercises.values());
  }

  async getExercise(id: string): Promise<Exercise | undefined> {
    return this.exercises.get(id);
  }

  async createExercise(insertExercise: InsertExercise): Promise<Exercise> {
    const id = randomUUID();
    const exercise: Exercise = { ...insertExercise, id };
    this.exercises.set(id, exercise);
    return exercise;
  }

  async searchExercises(query: string): Promise<Exercise[]> {
    const lowerQuery = query.toLowerCase();
    return Array.from(this.exercises.values()).filter(exercise =>
      exercise.name.toLowerCase().includes(lowerQuery) ||
      exercise.description.toLowerCase().includes(lowerQuery) ||
      exercise.primaryMuscleGroups.some(muscle => muscle.toLowerCase().includes(lowerQuery))
    );
  }

  async filterExercises(filters: {
    difficulty?: string;
    equipment?: string[];
    muscleGroup?: string;
    category?: string;
  }): Promise<Exercise[]> {
    return Array.from(this.exercises.values()).filter(exercise => {
      if (filters.difficulty && exercise.difficulty !== filters.difficulty) return false;
      if (filters.category && exercise.category !== filters.category) return false;
      if (filters.muscleGroup && !exercise.primaryMuscleGroups.includes(filters.muscleGroup)) return false;
      if (filters.equipment && !filters.equipment.some(eq => exercise.equipment.includes(eq))) return false;
      return true;
    });
  }

  // Workout session operations
  async createWorkoutSession(insertSession: InsertWorkoutSession): Promise<WorkoutSession> {
    const id = randomUUID();
    const session: WorkoutSession = {
      ...insertSession,
      id,
      startedAt: new Date(),
      completedAt: null,
      duration: null,
      isCompleted: false,
    };
    this.workoutSessions.set(id, session);
    return session;
  }

  async getWorkoutSession(id: string): Promise<WorkoutSession | undefined> {
    return this.workoutSessions.get(id);
  }

  async updateWorkoutSession(id: string, updates: Partial<WorkoutSession>): Promise<WorkoutSession | undefined> {
    const session = this.workoutSessions.get(id);
    if (!session) return undefined;
    
    const updatedSession = { ...session, ...updates };
    this.workoutSessions.set(id, updatedSession);
    return updatedSession;
  }

  async getUserWorkoutSessions(userId: string): Promise<WorkoutSession[]> {
    return Array.from(this.workoutSessions.values())
      .filter(session => session.userId === userId)
      .sort((a, b) => new Date(b.startedAt).getTime() - new Date(a.startedAt).getTime());
  }

  async getActiveWorkoutSession(userId: string): Promise<WorkoutSession | undefined> {
    return Array.from(this.workoutSessions.values())
      .find(session => session.userId === userId && !session.isCompleted);
  }

  // Session exercise operations
  async addExerciseToSession(insertSessionExercise: InsertSessionExercise): Promise<SessionExercise> {
    const id = randomUUID();
    const sessionExercise: SessionExercise = { ...insertSessionExercise, id };
    this.sessionExercises.set(id, sessionExercise);
    return sessionExercise;
  }

  async getSessionExercises(sessionId: string): Promise<SessionExercise[]> {
    return Array.from(this.sessionExercises.values())
      .filter(exercise => exercise.sessionId === sessionId)
      .sort((a, b) => a.orderInSession - b.orderInSession);
  }

  async updateSessionExercise(id: string, updates: Partial<SessionExercise>): Promise<SessionExercise | undefined> {
    const sessionExercise = this.sessionExercises.get(id);
    if (!sessionExercise) return undefined;
    
    const updated = { ...sessionExercise, ...updates };
    this.sessionExercises.set(id, updated);
    return updated;
  }

  async deleteSessionExercise(id: string): Promise<boolean> {
    return this.sessionExercises.delete(id);
  }

  // Exercise set operations
  async addExerciseSet(insertExerciseSet: InsertExerciseSet): Promise<ExerciseSet> {
    const id = randomUUID();
    const exerciseSet: ExerciseSet = {
      ...insertExerciseSet,
      id,
      isCompleted: false,
      completedAt: null,
      // Ensure weight is stored as number if provided
      weight: insertExerciseSet.weight ? Number(insertExerciseSet.weight) : undefined,
    };
    this.exerciseSets.set(id, exerciseSet);
    return exerciseSet;
  }

  async getExerciseSets(sessionExerciseId: string): Promise<ExerciseSet[]> {
    return Array.from(this.exerciseSets.values())
      .filter(set => set.sessionExerciseId === sessionExerciseId)
      .sort((a, b) => a.setNumber - b.setNumber);
  }

  async updateExerciseSet(id: string, updates: Partial<ExerciseSet>): Promise<ExerciseSet | undefined> {
    const exerciseSet = this.exerciseSets.get(id);
    if (!exerciseSet) return undefined;
    
    const updated = { 
      ...exerciseSet, 
      ...updates,
      // Ensure weight is converted to number if provided
      weight: updates.weight !== undefined ? (updates.weight ? Number(updates.weight) : undefined) : exerciseSet.weight,
    };
    if (updates.isCompleted && !exerciseSet.isCompleted) {
      updated.completedAt = new Date();
      
      // Auto-increment completedSets count when set is marked complete
      const sessionExercise = this.sessionExercises.get(exerciseSet.sessionExerciseId);
      if (sessionExercise) {
        const updatedSessionExercise = { 
          ...sessionExercise, 
          completedSets: sessionExercise.completedSets + 1 
        };
        this.sessionExercises.set(exerciseSet.sessionExerciseId, updatedSessionExercise);
      }
    }
    this.exerciseSets.set(id, updated);
    return updated;
  }

  async deleteExerciseSet(id: string): Promise<boolean> {
    return this.exerciseSets.delete(id);
  }

  // Chat session operations
  async createChatSession(insertSession: InsertChatSession): Promise<ChatSession> {
    const id = randomUUID();
    const now = new Date();
    const session: ChatSession = {
      ...insertSession,
      id,
      createdAt: now,
      updatedAt: now,
    };
    
    // Deactivate other sessions for this user if this is marked as active
    if (session.isActive) {
      await this.deactivateOtherSessions(session.userId, id);
    }
    
    this.chatSessions.set(id, session);
    return session;
  }

  async getChatSession(id: string): Promise<ChatSession | undefined> {
    return this.chatSessions.get(id);
  }

  async updateChatSession(id: string, updates: UpdateChatSession): Promise<ChatSession | undefined> {
    const session = this.chatSessions.get(id);
    if (!session) return undefined;
    
    const updatedSession = { 
      ...session, 
      ...updates,
      updatedAt: new Date(),
    };
    
    this.chatSessions.set(id, updatedSession);
    return updatedSession;
  }

  async getUserChatSessions(userId: string): Promise<ChatSession[]> {
    return Array.from(this.chatSessions.values())
      .filter(session => session.userId === userId)
      .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());
  }

  async getActiveChatSession(userId: string): Promise<ChatSession | undefined> {
    return Array.from(this.chatSessions.values())
      .find(session => session.userId === userId && session.isActive);
  }

  async deactivateOtherSessions(userId: string, activeSessionId: string): Promise<void> {
    for (const [id, session] of this.chatSessions.entries()) {
      if (session.userId === userId && session.id !== activeSessionId && session.isActive) {
        const updated = { ...session, isActive: false, updatedAt: new Date() };
        this.chatSessions.set(id, updated);
      }
    }
  }

  // Chat message operations
  async addChatMessage(insertMessage: InsertChatMessage): Promise<ChatMessage> {
    const id = randomUUID();
    const message: ChatMessage = {
      ...insertMessage,
      id,
      createdAt: new Date(),
    };
    
    // Update the session's updatedAt timestamp
    const session = this.chatSessions.get(message.sessionId);
    if (session) {
      const updatedSession = { ...session, updatedAt: new Date() };
      this.chatSessions.set(session.id, updatedSession);
    }
    
    this.chatMessages.set(id, message);
    return message;
  }

  async getChatMessages(sessionId: string, limit?: number): Promise<ChatMessage[]> {
    const messages = Array.from(this.chatMessages.values())
      .filter(message => message.sessionId === sessionId)
      .sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
    
    return limit ? messages.slice(-limit) : messages;
  }

  async deleteChatSession(sessionId: string): Promise<boolean> {
    const session = this.chatSessions.get(sessionId);
    if (!session) return false;
    
    // Delete all messages in this session first
    const messages = Array.from(this.chatMessages.values())
      .filter(message => message.sessionId === sessionId);
    
    for (const message of messages) {
      this.chatMessages.delete(message.id);
    }
    
    // Delete the session
    return this.chatSessions.delete(sessionId);
  }

  async deleteChatMessage(messageId: string): Promise<boolean> {
    return this.chatMessages.delete(messageId);
  }

  async getChatSessionWithMessages(sessionId: string, messageLimit?: number): Promise<(ChatSession & { messages: ChatMessage[] }) | undefined> {
    const session = await this.getChatSession(sessionId);
    if (!session) return undefined;
    
    const messages = await this.getChatMessages(sessionId, messageLimit);
    return { ...session, messages };
  }

  // Cleanup and retention operations
  async cleanupOldSessions(retentionDays: number = 30): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - retentionDays);
    
    console.log(`Starting cleanup: removing chat sessions older than ${retentionDays} days (before ${cutoffDate.toISOString()})`);
    
    let deletedCount = 0;
    const sessionsToDelete: string[] = [];
    
    for (const [id, session] of this.chatSessions.entries()) {
      if (new Date(session.updatedAt) < cutoffDate && !session.isActive) {
        sessionsToDelete.push(id);
      }
    }
    
    console.log(`Found ${sessionsToDelete.length} old sessions to delete`);
    
    for (const sessionId of sessionsToDelete) {
      if (await this.deleteChatSession(sessionId)) {
        deletedCount++;
      }
    }
    
    console.log(`Cleanup completed: deleted ${deletedCount} old chat sessions`);
    return deletedCount;
  }

  async cleanupOldMessages(retentionDays: number = 90): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - retentionDays);
    
    console.log(`Starting cleanup: removing chat messages older than ${retentionDays} days (before ${cutoffDate.toISOString()})`);
    
    let deletedCount = 0;
    const messagesToDelete: string[] = [];
    
    for (const [id, message] of this.chatMessages.entries()) {
      if (new Date(message.createdAt) < cutoffDate) {
        messagesToDelete.push(id);
      }
    }
    
    console.log(`Found ${messagesToDelete.length} old messages to delete`);
    
    for (const messageId of messagesToDelete) {
      if (this.chatMessages.delete(messageId)) {
        deletedCount++;
      }
    }
    
    console.log(`Cleanup completed: deleted ${deletedCount} old chat messages`);
    return deletedCount;
  }

  async getSessionStats(userId?: string): Promise<{ totalSessions: number; activeSessions: number; totalMessages: number; oldestSession?: Date; }> {
    const sessions = userId 
      ? Array.from(this.chatSessions.values()).filter(s => s.userId === userId)
      : Array.from(this.chatSessions.values());
    
    const messages = userId 
      ? Array.from(this.chatMessages.values()).filter(m => {
          const session = this.chatSessions.get(m.sessionId);
          return session?.userId === userId;
        })
      : Array.from(this.chatMessages.values());
    
    const activeSessions = sessions.filter(s => s.isActive).length;
    const oldestSession = sessions.length > 0 
      ? sessions.reduce((oldest, current) => 
          new Date(current.createdAt) < new Date(oldest.createdAt) ? current : oldest
        ).createdAt
      : undefined;
    
    return {
      totalSessions: sessions.length,
      activeSessions,
      totalMessages: messages.length,
      oldestSession: oldestSession ? new Date(oldestSession) : undefined
    };
  }

  // Helper methods for batching operations to fix N+1 patterns
  private async batchGetSessionExercises(sessionIds: string[]): Promise<SessionExercise[]> {
    return Array.from(this.sessionExercises.values())
      .filter(exercise => sessionIds.includes(exercise.sessionId))
      .sort((a, b) => a.orderInSession - b.orderInSession);
  }

  private async batchGetExerciseSets(sessionExerciseIds: string[]): Promise<ExerciseSet[]> {
    return Array.from(this.exerciseSets.values())
      .filter(set => sessionExerciseIds.includes(set.sessionExerciseId))
      .sort((a, b) => a.setNumber - b.setNumber);
  }

  private groupBy<T>(array: T[], key: keyof T): Record<string, T[]> {
    return array.reduce((groups, item) => {
      const groupKey = String(item[key]);
      if (!groups[groupKey]) {
        groups[groupKey] = [];
      }
      groups[groupKey].push(item);
      return groups;
    }, {} as Record<string, T[]>);
  }

  // Optimized analytics operations implementation with batching to fix N+1 patterns
  async getAnalyticsOverview(userId: string, dateRange?: DateRange): Promise<AnalyticsOverview> {
    const sessions = await this.getUserWorkoutSessions(userId);
    const filteredSessions = dateRange 
      ? sessions.filter(s => {
          const sessionDate = new Date(s.startedAt);
          return sessionDate >= dateRange.startDate && sessionDate <= dateRange.endDate;
        })
      : sessions;

    const completedSessions = filteredSessions.filter(s => s.isCompleted);
    
    // Batch fetch all session exercises and sets to avoid N+1 queries
    const sessionIds = completedSessions.map(s => s.id);
    const allSessionExercises = await this.batchGetSessionExercises(sessionIds);
    const allSets = await this.batchGetExerciseSets(allSessionExercises.map(e => e.id));
    
    // Build lookup maps for efficient access
    const exercisesBySession = this.groupBy(allSessionExercises, 'sessionId');
    const setsByExercise = this.groupBy(allSets, 'sessionExerciseId');
    
    // Calculate total volume
    let totalVolume = 0;
    let exerciseCount: Record<string, number> = {};
    
    for (const session of completedSessions) {
      const sessionExercises = exercisesBySession[session.id] || [];
      for (const sessionExercise of sessionExercises) {
        const sets = setsByExercise[sessionExercise.id] || [];
        exerciseCount[sessionExercise.exerciseName] = (exerciseCount[sessionExercise.exerciseName] || 0) + 1;
        
        for (const set of sets.filter(s => s.isCompleted)) {
          const volume = (set.reps || 0) * (Number(set.weight) || 0);
          totalVolume += volume;
        }
      }
    }

    // Calculate streaks
    const sortedSessions = completedSessions.sort((a, b) => new Date(a.startedAt).getTime() - new Date(b.startedAt).getTime());
    let currentStreak = 0;
    let longestStreak = 0;
    let tempStreak = 0;
    
    const today = new Date();
    for (let i = 0; i < sortedSessions.length; i++) {
      const sessionDate = new Date(sortedSessions[i].startedAt);
      if (i === 0 || this.daysDifference(new Date(sortedSessions[i-1].startedAt), sessionDate) === 1) {
        tempStreak++;
      } else {
        tempStreak = 1;
      }
      longestStreak = Math.max(longestStreak, tempStreak);
      
      // Check if this session contributes to current streak
      if (this.daysDifference(sessionDate, today) <= 1) {
        currentStreak = tempStreak;
      }
    }

    // Get personal records count
    const prs = await this.getPersonalRecords(userId);
    
    // Calculate workout consistency
    const profile = await this.getUserProfile(userId);
    const targetFrequency = profile?.weeklyWorkoutFrequency || 3;
    const weeksInRange = dateRange ? this.weeksBetween(dateRange.startDate, dateRange.endDate) : 12;
    const expectedWorkouts = targetFrequency * weeksInRange;
    const workoutConsistency = expectedWorkouts > 0 ? Math.min(100, (completedSessions.length / expectedWorkouts) * 100) : 0;

    // Find favorite exercise
    const favoriteExercise = Object.entries(exerciseCount).sort(([,a], [,b]) => b - a)[0]?.[0];

    return {
      totalWorkouts: completedSessions.length,
      totalDuration: Math.round(completedSessions.reduce((sum, s) => sum + (s.duration || 0), 0) / 60), // Convert seconds to minutes
      currentStreak,
      longestStreak,
      totalVolume: Math.round(totalVolume),
      personalRecords: prs.length,
      averageWorkoutDuration: completedSessions.length > 0 
        ? Math.round(completedSessions.reduce((sum, s) => sum + (s.duration || 0), 0) / completedSessions.length / 60) // Convert seconds to minutes
        : 0,
      completedGoals: 0, // TODO: Implement based on goal progress
      workoutConsistency: Math.round(workoutConsistency),
      favoriteExercise
    };
  }

  async getWorkoutTrends(userId: string, dateRange: DateRange, groupBy: 'day' | 'week' | 'month'): Promise<WorkoutTrend[]> {
    const sessions = await this.getUserWorkoutSessions(userId);
    const filteredSessions = sessions.filter(s => {
      const sessionDate = new Date(s.startedAt);
      return sessionDate >= dateRange.startDate && sessionDate <= dateRange.endDate && s.isCompleted;
    });

    // Batch fetch all session exercises and sets to avoid N+1 queries
    const sessionIds = filteredSessions.map(s => s.id);
    const allSessionExercises = await this.batchGetSessionExercises(sessionIds);
    const allSets = await this.batchGetExerciseSets(allSessionExercises.map(e => e.id));
    
    // Build lookup maps for efficient access
    const exercisesBySession = this.groupBy(allSessionExercises, 'sessionId');
    const setsByExercise = this.groupBy(allSets, 'sessionExerciseId');

    const trends: Record<string, { workouts: number; duration: number; volume: number }> = {};

    for (const session of filteredSessions) {
      const dateKey = this.formatDateForGrouping(new Date(session.startedAt), groupBy);
      
      if (!trends[dateKey]) {
        trends[dateKey] = { workouts: 0, duration: 0, volume: 0 };
      }

      trends[dateKey].workouts += 1;
      trends[dateKey].duration += session.duration || 0;

      // Calculate volume for this session using batch data
      const sessionExercises = exercisesBySession[session.id] || [];
      for (const sessionExercise of sessionExercises) {
        const sets = setsByExercise[sessionExercise.id] || [];
        for (const set of sets.filter(s => s.isCompleted)) {
          const volume = (set.reps || 0) * (Number(set.weight) || 0);
          trends[dateKey].volume += volume;
        }
      }
    }

    return Object.entries(trends)
      .map(([date, data]) => ({
        date,
        workouts: data.workouts,
        duration: Math.round(data.duration / 60), // Convert seconds to minutes
        volume: Math.round(data.volume)
      }))
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  }

  async getVolumeProgression(userId: string, exercise?: string, dateRange?: DateRange): Promise<VolumeProgression[]> {
    const sessions = await this.getUserWorkoutSessions(userId);
    const filteredSessions = sessions.filter(s => {
      if (!s.isCompleted) return false;
      if (dateRange) {
        const sessionDate = new Date(s.startedAt);
        return sessionDate >= dateRange.startDate && sessionDate <= dateRange.endDate;
      }
      return true;
    });

    // Batch fetch all session exercises and sets to avoid N+1 queries
    const sessionIds = filteredSessions.map(s => s.id);
    const allSessionExercises = await this.batchGetSessionExercises(sessionIds);
    const allSets = await this.batchGetExerciseSets(allSessionExercises.map(e => e.id));
    
    // Build lookup maps for efficient access
    const exercisesBySession = this.groupBy(allSessionExercises, 'sessionId');
    const setsByExercise = this.groupBy(allSets, 'sessionExerciseId');

    const progressions: VolumeProgression[] = [];

    for (const session of filteredSessions) {
      const sessionExercises = exercisesBySession[session.id] || [];
      const filteredExercises = exercise 
        ? sessionExercises.filter(e => e.exerciseName.toLowerCase().includes(exercise.toLowerCase()))
        : sessionExercises;

      for (const sessionExercise of filteredExercises) {
        const sets = setsByExercise[sessionExercise.id] || [];
        const completedSets = sets.filter(s => s.isCompleted);
        
        if (completedSets.length > 0) {
          const totalVolume = completedSets.reduce((sum, set) => {
            return sum + (set.reps || 0) * (Number(set.weight) || 0);
          }, 0);

          // Calculate estimated 1RM using Epley formula
          const maxSet = completedSets.reduce((max, set) => {
            const weight = Number(set.weight) || 0;
            const reps = set.reps || 0;
            const estimatedMax = weight * (1 + reps / 30);
            return estimatedMax > (max.estimatedMax || 0) ? { ...set, estimatedMax } : max;
          }, {} as any);

          progressions.push({
            exercise: sessionExercise.exerciseName,
            date: session.startedAt.toISOString().split('T')[0],
            volume: Math.round(totalVolume),
            oneRepMax: maxSet.estimatedMax ? Math.round(maxSet.estimatedMax) : undefined
          });
        }
      }
    }

    return progressions.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  }

  async getPersonalRecords(userId: string, exercise?: string, limit?: number): Promise<PersonalRecord[]> {
    const sessions = await this.getUserWorkoutSessions(userId);
    const completedSessions = sessions.filter(s => s.isCompleted);
    
    // Batch fetch all session exercises and sets to avoid N+1 queries
    const sessionIds = completedSessions.map(s => s.id);
    const allSessionExercises = await this.batchGetSessionExercises(sessionIds);
    const allSets = await this.batchGetExerciseSets(allSessionExercises.map(e => e.id));
    
    // Build lookup maps for efficient access
    const exercisesBySession = this.groupBy(allSessionExercises, 'sessionId');
    const setsByExercise = this.groupBy(allSets, 'sessionExerciseId');
    
    const exerciseRecords: Record<string, PersonalRecord[]> = {};

    for (const session of completedSessions) {
      const sessionExercises = exercisesBySession[session.id] || [];
      const filteredExercises = exercise 
        ? sessionExercises.filter(e => e.exerciseName.toLowerCase().includes(exercise.toLowerCase()))
        : sessionExercises;

      for (const sessionExercise of filteredExercises) {
        const sets = setsByExercise[sessionExercise.id] || [];
        const completedSets = sets.filter(s => s.isCompleted && s.weight && s.reps);

        for (const set of completedSets) {
          const weight = Number(set.weight);
          const reps = set.reps!;
          const exerciseName = sessionExercise.exerciseName;

          if (!exerciseRecords[exerciseName]) {
            exerciseRecords[exerciseName] = [];
          }

          const existingPR = exerciseRecords[exerciseName].find(pr => pr.reps === reps);
          
          if (!existingPR || weight > existingPR.weight) {
            const newPR: PersonalRecord = {
              id: `${exerciseName}-${reps}reps`,
              exercise: exerciseName,
              weight,
              reps,
              achievedAt: (set.completedAt || session.startedAt).toISOString(), // Convert to string for JSON safety
              previousBest: existingPR ? {
                weight: existingPR.weight,
                reps: existingPR.reps,
                date: existingPR.achievedAt // Already a string from previous conversion
              } : undefined
            };

            if (existingPR) {
              const index = exerciseRecords[exerciseName].findIndex(pr => pr.reps === reps);
              exerciseRecords[exerciseName][index] = newPR;
            } else {
              exerciseRecords[exerciseName].push(newPR);
            }
          }
        }
      }
    }

    const allRecords = Object.values(exerciseRecords).flat();
    const sortedRecords = allRecords.sort((a, b) => new Date(b.achievedAt).getTime() - new Date(a.achievedAt).getTime());
    
    return limit ? sortedRecords.slice(0, limit) : sortedRecords;
  }

  async getMuscleGroupDistribution(userId: string, dateRange?: DateRange): Promise<MuscleGroupDistribution[]> {
    const sessions = await this.getUserWorkoutSessions(userId);
    const filteredSessions = sessions.filter(s => {
      if (!s.isCompleted) return false;
      if (dateRange) {
        const sessionDate = new Date(s.startedAt);
        return sessionDate >= dateRange.startDate && sessionDate <= dateRange.endDate;
      }
      return true;
    });

    // Batch fetch all session exercises to avoid N+1 queries
    const sessionIds = filteredSessions.map(s => s.id);
    const allSessionExercises = await this.batchGetSessionExercises(sessionIds);
    const exercisesBySession = this.groupBy(allSessionExercises, 'sessionId');

    const muscleGroupCount: Record<string, number> = {};
    let totalSessions = 0;

    for (const session of filteredSessions) {
      const sessionExercises = exercisesBySession[session.id] || [];
      const sessionMuscleGroups = new Set<string>();

      // Get muscle groups from exercise names (basic mapping)
      for (const sessionExercise of sessionExercises) {
        const muscleGroups = this.inferMuscleGroupsFromExercise(sessionExercise.exerciseName);
        muscleGroups.forEach(mg => sessionMuscleGroups.add(mg));
      }

      sessionMuscleGroups.forEach(mg => {
        muscleGroupCount[mg] = (muscleGroupCount[mg] || 0) + 1;
      });
      totalSessions++;
    }

    return Object.entries(muscleGroupCount)
      .map(([muscleGroup, sessions]) => ({
        muscleGroup,
        sessions,
        percentage: Math.round((sessions / Math.max(totalSessions, 1)) * 100)
      }))
      .sort((a, b) => b.sessions - a.sessions);
  }

  async getGoalProgress(userId: string): Promise<GoalProgress[]> {
    const profile = await this.getUserProfile(userId);
    if (!profile || !profile.goals.length) return [];

    const overview = await this.getAnalyticsOverview(userId);
    const progress: GoalProgress[] = [];

    for (const goal of profile.goals) {
      let current = 0;
      let target = 100;
      let onTrack = false;

      switch (goal) {
        case 'weight_loss':
          if (profile.weight && profile.targetWeight) {
            const startWeight = Number(profile.weight);
            const targetWeight = Number(profile.targetWeight);
            const weightLoss = startWeight - targetWeight;
            current = Math.max(0, startWeight - targetWeight); // Assuming current weight tracking isn't implemented
            target = weightLoss;
            onTrack = current >= target * 0.7; // 70% progress is considered on track
          }
          break;
        case 'muscle_gain':
          // Use volume progression as proxy for muscle gain
          current = overview.totalVolume;
          target = overview.totalVolume * 1.5; // Target 50% volume increase
          onTrack = current >= target * 0.6;
          break;
        case 'strength':
          current = overview.personalRecords;
          target = Math.max(10, overview.personalRecords + 5); // Target more PRs
          onTrack = current >= target * 0.8;
          break;
        case 'endurance':
          current = overview.averageWorkoutDuration;
          target = Math.max(60, overview.averageWorkoutDuration + 15); // Target longer workouts
          onTrack = current >= target * 0.9;
          break;
        case 'general_fitness':
          current = overview.workoutConsistency;
          target = 85; // Target 85% consistency
          onTrack = current >= target * 0.9;
          break;
      }

      progress.push({
        goalType: goal,
        target,
        current,
        percentage: Math.round((current / target) * 100),
        onTrack
      });
    }

    return progress;
  }

  async getAchievements(userId: string, limit?: number): Promise<Achievement[]> {
    const overview = await this.getAnalyticsOverview(userId);
    const achievements: Achievement[] = [];

    // Streak achievements
    if (overview.currentStreak >= 7) {
      achievements.push({
        id: 'streak-7',
        type: 'streak',
        title: 'Week Warrior',
        description: 'Worked out for 7 days straight',
        achievedAt: new Date(), // This should be tracked properly in real implementation
        value: overview.currentStreak
      });
    }

    if (overview.longestStreak >= 30) {
      achievements.push({
        id: 'streak-30',
        type: 'streak',
        title: 'Month Master',
        description: 'Maintained a 30-day workout streak',
        achievedAt: new Date(),
        value: overview.longestStreak
      });
    }

    // Volume milestones
    if (overview.totalVolume >= 10000) {
      achievements.push({
        id: 'volume-10k',
        type: 'volume',
        title: 'Volume Veteran',
        description: 'Lifted over 10,000 total volume',
        achievedAt: new Date(),
        value: overview.totalVolume
      });
    }

    // Consistency achievements
    if (overview.workoutConsistency >= 90) {
      achievements.push({
        id: 'consistency-90',
        type: 'consistency',
        title: 'Consistency Champion',
        description: 'Maintained 90%+ workout consistency',
        achievedAt: new Date(),
        value: overview.workoutConsistency
      });
    }

    // Workout count milestones
    if (overview.totalWorkouts >= 100) {
      achievements.push({
        id: 'workouts-100',
        type: 'milestone',
        title: 'Century Club',
        description: 'Completed 100 workouts',
        achievedAt: new Date(),
        value: overview.totalWorkouts
      });
    }

    const sortedAchievements = achievements.sort((a, b) => new Date(b.achievedAt).getTime() - new Date(a.achievedAt).getTime());
    return limit ? sortedAchievements.slice(0, limit) : sortedAchievements;
  }

  // Helper methods
  private daysDifference(date1: Date, date2: Date): number {
    const diffTime = Math.abs(date2.getTime() - date1.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  private weeksBetween(startDate: Date, endDate: Date): number {
    const diffTime = endDate.getTime() - startDate.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24 * 7));
  }

  private formatDateForGrouping(date: Date, groupBy: 'day' | 'week' | 'month'): string {
    switch (groupBy) {
      case 'day':
        return date.toISOString().split('T')[0];
      case 'week':
        const weekStart = new Date(date);
        weekStart.setDate(date.getDate() - date.getDay());
        return weekStart.toISOString().split('T')[0];
      case 'month':
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      default:
        return date.toISOString().split('T')[0];
    }
  }

  private inferMuscleGroupsFromExercise(exerciseName: string): string[] {
    const name = exerciseName.toLowerCase();
    const muscleGroups: string[] = [];

    // Basic muscle group inference based on exercise name
    if (name.includes('bench') || name.includes('press') || name.includes('chest')) {
      muscleGroups.push('chest');
    }
    if (name.includes('squat') || name.includes('leg press') || name.includes('quad')) {
      muscleGroups.push('quadriceps');
    }
    if (name.includes('deadlift') || name.includes('row') || name.includes('pull') || name.includes('back')) {
      muscleGroups.push('back');
    }
    if (name.includes('curl') || name.includes('bicep')) {
      muscleGroups.push('biceps');
    }
    if (name.includes('shoulder') || name.includes('lateral') || name.includes('overhead')) {
      muscleGroups.push('shoulders');
    }
    if (name.includes('tricep') || name.includes('dip') || name.includes('extension')) {
      muscleGroups.push('triceps');
    }
    if (name.includes('leg curl') || name.includes('hamstring')) {
      muscleGroups.push('hamstrings');
    }
    if (name.includes('calf')) {
      muscleGroups.push('calves');
    }
    if (name.includes('glute') || name.includes('hip thrust')) {
      muscleGroups.push('glutes');
    }
    if (name.includes('ab') || name.includes('core') || name.includes('plank')) {
      muscleGroups.push('abs');
    }

    return muscleGroups.length > 0 ? muscleGroups : ['full body'];
  }

  // Seed basic food data
  private seedFoods(): void {
    const basicFoods = [
      // Proteins
      { name: "Chicken Breast", category: "protein", calories: 165, protein: 31, carbs: 0, fat: 3.6 },
      { name: "Salmon", category: "protein", calories: 208, protein: 20, carbs: 0, fat: 12 },
      { name: "Eggs", category: "protein", calories: 155, protein: 13, carbs: 1.1, fat: 11 },
      { name: "Greek Yogurt", category: "dairy", calories: 59, protein: 10, carbs: 3.6, fat: 0.4 },
      { name: "Tuna", category: "protein", calories: 130, protein: 28, carbs: 0, fat: 0.8 },
      
      // Carbohydrates
      { name: "Brown Rice", category: "grain", calories: 112, protein: 2.6, carbs: 23, fat: 0.9 },
      { name: "Oats", category: "grain", calories: 389, protein: 16.9, carbs: 66.3, fat: 6.9 },
      { name: "Sweet Potato", category: "vegetable", calories: 86, protein: 1.6, carbs: 20, fat: 0.1 },
      { name: "Banana", category: "fruit", calories: 89, protein: 1.1, carbs: 23, fat: 0.3 },
      { name: "Apple", category: "fruit", calories: 52, protein: 0.3, carbs: 14, fat: 0.2 },
      
      // Vegetables
      { name: "Broccoli", category: "vegetable", calories: 34, protein: 2.8, carbs: 7, fat: 0.4 },
      { name: "Spinach", category: "vegetable", calories: 23, protein: 2.9, carbs: 3.6, fat: 0.4 },
      { name: "Avocado", category: "fruit", calories: 160, protein: 2, carbs: 9, fat: 15 },
      
      // Nuts and Seeds
      { name: "Almonds", category: "snack", calories: 579, protein: 21, carbs: 22, fat: 50 },
      { name: "Peanut Butter", category: "condiment", calories: 588, protein: 25, carbs: 20, fat: 50 },
      
      // Beverages
      { name: "Milk", category: "dairy", calories: 42, protein: 3.4, carbs: 5, fat: 1 },
      { name: "Water", category: "beverage", calories: 0, protein: 0, carbs: 0, fat: 0 },
    ];

    basicFoods.forEach((food, index) => {
      const id = `food-${index + 1}`;
      const now = new Date();
      
      const foodRecord: Food = {
        id,
        name: food.name,
        brand: null,
        category: food.category as any,
        caloriesPerHundredGrams: String(food.calories),
        proteinPerHundredGrams: String(food.protein),
        carbsPerHundredGrams: String(food.carbs),
        fatPerHundredGrams: String(food.fat),
        fiberPerHundredGrams: "0",
        sodiumPerHundredGrams: "0",
        isUserCreated: false,
        createdBy: null,
        barcode: null,
        description: null,
        createdAt: now,
        updatedAt: now,
      };
      
      this.foods.set(id, foodRecord);
      
      // Add common portions for each food
      const commonPortions = [
        { name: "100g", grams: 100, isDefault: true },
        { name: "1 serving", grams: 100, isDefault: false },
      ];
      
      // Food-specific portions
      if (food.name === "Chicken Breast") {
        commonPortions.push({ name: "1 breast (120g)", grams: 120, isDefault: false });
      } else if (food.name === "Eggs") {
        commonPortions.push({ name: "1 large egg (50g)", grams: 50, isDefault: false });
      } else if (food.name === "Banana") {
        commonPortions.push({ name: "1 medium banana (120g)", grams: 120, isDefault: false });
      } else if (food.name === "Apple") {
        commonPortions.push({ name: "1 medium apple (180g)", grams: 180, isDefault: false });
      }
      
      commonPortions.forEach((portion, portionIndex) => {
        const portionId = `${id}-portion-${portionIndex + 1}`;
        const portionRecord: FoodPortion = {
          id: portionId,
          foodId: id,
          name: portion.name,
          gramsEquivalent: String(portion.grams),
          isDefault: portion.isDefault,
        };
        this.foodPortions.set(portionId, portionRecord);
      });
    });
  }

  // Seed basic exercise data
  private seedExercises(): void {
    const basicExercises = [
      // Chest Exercises
      {
        name: "Push-ups",
        description: "Classic bodyweight chest exercise",
        instructions: [
          "Start in a plank position with hands slightly wider than shoulders",
          "Lower your body until chest nearly touches the floor",
          "Push back up to starting position",
          "Keep your body in a straight line throughout"
        ],
        primaryMuscleGroups: ["chest"],
        secondaryMuscleGroups: ["triceps", "shoulders"],
        difficulty: "beginner",
        equipment: ["bodyweight"],
        category: "strength",
        safetyTips: ["Keep core engaged", "Don't let hips sag"]
      },
      {
        name: "Bench Press",
        description: "Fundamental chest building exercise",
        instructions: [
          "Lie on bench with feet flat on floor",
          "Grip bar slightly wider than shoulder width",
          "Lower bar to chest with control",
          "Press bar back to starting position"
        ],
        primaryMuscleGroups: ["chest"],
        secondaryMuscleGroups: ["triceps", "shoulders"],
        difficulty: "intermediate",
        equipment: ["barbell", "bench"],
        category: "strength",
        safetyTips: ["Use a spotter", "Keep feet on floor", "Control the weight"]
      },

      // Back Exercises
      {
        name: "Pull-ups",
        description: "Upper body pulling exercise",
        instructions: [
          "Hang from pull-up bar with overhand grip",
          "Pull your body up until chin clears the bar",
          "Lower yourself with control",
          "Keep core engaged throughout"
        ],
        primaryMuscleGroups: ["back"],
        secondaryMuscleGroups: ["biceps", "shoulders"],
        difficulty: "intermediate",
        equipment: ["pull-up bar"],
        category: "strength",
        safetyTips: ["Don't swing", "Full range of motion"]
      },
      {
        name: "Bent-over Row",
        description: "Compound back exercise",
        instructions: [
          "Stand with feet hip-width apart, holding barbell",
          "Hinge at hips, keep back straight",
          "Pull bar to lower chest/upper abdomen",
          "Lower with control"
        ],
        primaryMuscleGroups: ["back"],
        secondaryMuscleGroups: ["biceps", "shoulders"],
        difficulty: "intermediate",
        equipment: ["barbell"],
        category: "strength",
        safetyTips: ["Keep back straight", "Don't round shoulders"]
      },

      // Leg Exercises
      {
        name: "Squats",
        description: "Fundamental lower body exercise",
        instructions: [
          "Stand with feet shoulder-width apart",
          "Lower hips back and down as if sitting in a chair",
          "Keep chest up and knees tracking over toes",
          "Drive through heels to return to standing"
        ],
        primaryMuscleGroups: ["quadriceps", "glutes"],
        secondaryMuscleGroups: ["hamstrings", "calves"],
        difficulty: "beginner",
        equipment: ["bodyweight"],
        category: "strength",
        safetyTips: ["Keep knees aligned", "Don't let knees cave inward"]
      },
      {
        name: "Deadlift",
        description: "Full-body compound movement",
        instructions: [
          "Stand with feet hip-width apart, bar over mid-foot",
          "Hinge at hips and knees to grip bar",
          "Keep back straight, chest up",
          "Drive through heels to lift bar, extending hips and knees"
        ],
        primaryMuscleGroups: ["hamstrings", "glutes", "back"],
        secondaryMuscleGroups: ["quadriceps", "forearms"],
        difficulty: "advanced",
        equipment: ["barbell"],
        category: "strength",
        safetyTips: ["Keep bar close to body", "Don't round back"]
      },

      // Shoulder Exercises
      {
        name: "Overhead Press",
        description: "Vertical pressing movement",
        instructions: [
          "Stand with feet shoulder-width apart",
          "Hold bar at shoulder height",
          "Press bar straight overhead",
          "Lower with control to starting position"
        ],
        primaryMuscleGroups: ["shoulders"],
        secondaryMuscleGroups: ["triceps"],
        difficulty: "intermediate",
        equipment: ["barbell"],
        category: "strength",
        safetyTips: ["Keep core tight", "Don't arch back excessively"]
      },

      // Arm Exercises
      {
        name: "Bicep Curls",
        description: "Isolation exercise for biceps",
        instructions: [
          "Stand with dumbbells at your sides",
          "Keep elbows close to your body",
          "Curl weights up to shoulder level",
          "Lower with control"
        ],
        primaryMuscleGroups: ["biceps"],
        secondaryMuscleGroups: [],
        difficulty: "beginner",
        equipment: ["dumbbell"],
        category: "strength",
        safetyTips: ["Don't swing weights", "Control the movement"]
      },

      // Core Exercises
      {
        name: "Plank",
        description: "Isometric core strengthening exercise",
        instructions: [
          "Start in push-up position",
          "Lower to forearms",
          "Keep body in straight line from head to heels",
          "Hold position while breathing normally"
        ],
        primaryMuscleGroups: ["abs"],
        secondaryMuscleGroups: ["shoulders", "back"],
        difficulty: "beginner",
        equipment: ["bodyweight"],
        category: "strength",
        safetyTips: ["Don't let hips sag", "Keep breathing"]
      },

      // Cardio Exercises
      {
        name: "Burpees",
        description: "Full-body cardio exercise",
        instructions: [
          "Start standing, then squat down",
          "Place hands on floor and jump feet back to plank",
          "Do a push-up (optional)",
          "Jump feet back to squat, then jump up with arms overhead"
        ],
        primaryMuscleGroups: ["full body"],
        secondaryMuscleGroups: [],
        difficulty: "intermediate",
        equipment: ["bodyweight"],
        category: "cardio",
        safetyTips: ["Land softly", "Modify as needed"]
      }
    ];

    basicExercises.forEach((exercise, index) => {
      const id = `exercise-${index + 1}`;
      const exerciseRecord: Exercise = {
        id,
        name: exercise.name,
        description: exercise.description,
        instructions: exercise.instructions,
        primaryMuscleGroups: exercise.primaryMuscleGroups as any[],
        secondaryMuscleGroups: exercise.secondaryMuscleGroups as any[],
        difficulty: exercise.difficulty as any,
        equipment: exercise.equipment,
        category: exercise.category as any,
        safetyTips: exercise.safetyTips,
        videoUrl: null,
        imageUrl: null,
      };

      this.exercises.set(id, exerciseRecord);
    });
  }

  // Helper function to calculate nutrition values
  private calculateNutritionFromGrams(food: Food, grams: number): { calories: number; protein: number; carbs: number; fat: number } {
    const ratio = grams / 100; // nutrition values are per 100g
    return {
      calories: Number(food.caloriesPerHundredGrams) * ratio,
      protein: Number(food.proteinPerHundredGrams) * ratio,
      carbs: Number(food.carbsPerHundredGrams) * ratio,
      fat: Number(food.fatPerHundredGrams) * ratio,
    };
  }

  // Food CRUD operations
  async getAllFoods(): Promise<Food[]> {
    return Array.from(this.foods.values());
  }

  async getFood(id: string): Promise<Food | undefined> {
    return this.foods.get(id);
  }

  async createFood(insertFood: InsertFood): Promise<Food> {
    const id = randomUUID();
    const now = new Date();
    const food: Food = {
      ...insertFood,
      id,
      caloriesPerHundredGrams: String(insertFood.caloriesPerHundredGrams),
      proteinPerHundredGrams: String(insertFood.proteinPerHundredGrams),
      carbsPerHundredGrams: String(insertFood.carbsPerHundredGrams),
      fatPerHundredGrams: String(insertFood.fatPerHundredGrams),
      fiberPerHundredGrams: String(insertFood.fiberPerHundredGrams || 0),
      sodiumPerHundredGrams: String(insertFood.sodiumPerHundredGrams || 0),
      createdAt: now,
      updatedAt: now,
    };
    this.foods.set(id, food);
    return food;
  }

  async searchFoods(query: string): Promise<Food[]> {
    const lowerQuery = query.toLowerCase();
    return Array.from(this.foods.values()).filter(food => 
      food.name.toLowerCase().includes(lowerQuery) ||
      (food.brand && food.brand.toLowerCase().includes(lowerQuery)) ||
      food.category.toLowerCase().includes(lowerQuery)
    );
  }

  async getUserCreatedFoods(userId: string): Promise<Food[]> {
    return Array.from(this.foods.values()).filter(food => 
      food.isUserCreated && food.createdBy === userId
    );
  }

  async updateFood(id: string, updates: Partial<Food>): Promise<Food | undefined> {
    const food = this.foods.get(id);
    if (!food) return undefined;
    
    const updatedFood = { ...food, ...updates, updatedAt: new Date() };
    this.foods.set(id, updatedFood);
    return updatedFood;
  }

  async deleteFood(id: string): Promise<boolean> {
    return this.foods.delete(id);
  }

  // Food portion operations
  async getFoodPortions(foodId: string): Promise<FoodPortion[]> {
    return Array.from(this.foodPortions.values()).filter(portion => 
      portion.foodId === foodId
    );
  }

  async createFoodPortion(insertPortion: InsertFoodPortion): Promise<FoodPortion> {
    const id = randomUUID();
    const portion: FoodPortion = {
      ...insertPortion,
      id,
      gramsEquivalent: String(insertPortion.gramsEquivalent),
    };
    this.foodPortions.set(id, portion);
    return portion;
  }

  async deleteFoodPortion(id: string): Promise<boolean> {
    return this.foodPortions.delete(id);
  }

  // Meal operations
  async getMealsByDate(userId: string, date: string): Promise<Meal[]> {
    return Array.from(this.meals.values()).filter(meal => 
      meal.userId === userId && meal.date === date
    ).sort((a, b) => {
      const mealOrder = { breakfast: 1, lunch: 2, dinner: 3, snack: 4 };
      return (mealOrder[a.mealType as keyof typeof mealOrder] || 5) - 
             (mealOrder[b.mealType as keyof typeof mealOrder] || 5);
    });
  }

  async getMeal(id: string): Promise<Meal | undefined> {
    return this.meals.get(id);
  }

  async createMeal(insertMeal: InsertMeal): Promise<Meal> {
    const id = randomUUID();
    const now = new Date();
    const meal: Meal = {
      ...insertMeal,
      id,
      totalCalories: "0",
      totalProtein: "0",
      totalCarbs: "0",
      totalFat: "0",
      createdAt: now,
      updatedAt: now,
    };
    this.meals.set(id, meal);
    return meal;
  }

  async updateMeal(id: string, updates: UpdateMeal): Promise<Meal | undefined> {
    const meal = this.meals.get(id);
    if (!meal) return undefined;
    
    const updatedMeal = { ...meal, ...updates, updatedAt: new Date() };
    this.meals.set(id, updatedMeal);
    return updatedMeal;
  }

  async deleteMeal(id: string): Promise<boolean> {
    // Also delete associated meal items
    const items = Array.from(this.mealItems.values()).filter(item => item.mealId === id);
    items.forEach(item => this.mealItems.delete(item.id));
    
    return this.meals.delete(id);
  }

  // Meal item operations
  async getMealItems(mealId: string): Promise<MealItem[]> {
    return Array.from(this.mealItems.values()).filter(item => 
      item.mealId === mealId
    );
  }

  async addMealItem(insertMealItem: InsertMealItem): Promise<MealItem> {
    const id = randomUUID();
    const food = this.foods.get(insertMealItem.foodId);
    if (!food) throw new Error('Food not found');
    
    const grams = Number(insertMealItem.grams);
    const nutrition = this.calculateNutritionFromGrams(food, grams);
    
    const mealItem: MealItem = {
      ...insertMealItem,
      id,
      grams: String(grams),
      calories: String(nutrition.calories.toFixed(2)),
      protein: String(nutrition.protein.toFixed(2)),
      carbs: String(nutrition.carbs.toFixed(2)),
      fat: String(nutrition.fat.toFixed(2)),
      createdAt: new Date(),
    };
    
    this.mealItems.set(id, mealItem);
    
    // Update meal totals
    await this.updateMealTotals(insertMealItem.mealId);
    
    return mealItem;
  }

  async updateMealItem(id: string, updates: UpdateMealItem): Promise<MealItem | undefined> {
    const mealItem = this.mealItems.get(id);
    if (!mealItem) return undefined;
    
    if (updates.grams) {
      const food = this.foods.get(mealItem.foodId);
      if (!food) throw new Error('Food not found');
      
      const grams = Number(updates.grams);
      const nutrition = this.calculateNutritionFromGrams(food, grams);
      
      const updatedItem = {
        ...mealItem,
        grams: String(grams),
        calories: String(nutrition.calories.toFixed(2)),
        protein: String(nutrition.protein.toFixed(2)),
        carbs: String(nutrition.carbs.toFixed(2)),
        fat: String(nutrition.fat.toFixed(2)),
      };
      
      this.mealItems.set(id, updatedItem);
      
      // Update meal totals
      await this.updateMealTotals(mealItem.mealId);
      
      return updatedItem;
    }
    
    return mealItem;
  }

  async deleteMealItem(id: string): Promise<boolean> {
    const mealItem = this.mealItems.get(id);
    if (!mealItem) return false;
    
    const deleted = this.mealItems.delete(id);
    if (deleted) {
      // Update meal totals
      await this.updateMealTotals(mealItem.mealId);
    }
    
    return deleted;
  }

  // Helper method to update meal totals
  private async updateMealTotals(mealId: string): Promise<void> {
    const meal = this.meals.get(mealId);
    if (!meal) return;
    
    const items = await this.getMealItems(mealId);
    const totals = items.reduce((acc, item) => ({
      calories: acc.calories + Number(item.calories),
      protein: acc.protein + Number(item.protein),
      carbs: acc.carbs + Number(item.carbs),
      fat: acc.fat + Number(item.fat),
    }), { calories: 0, protein: 0, carbs: 0, fat: 0 });
    
    const updatedMeal = {
      ...meal,
      totalCalories: String(totals.calories.toFixed(2)),
      totalProtein: String(totals.protein.toFixed(2)),
      totalCarbs: String(totals.carbs.toFixed(2)),
      totalFat: String(totals.fat.toFixed(2)),
      updatedAt: new Date(),
    };
    
    this.meals.set(mealId, updatedMeal);
    
    // Update daily nutrition summary
    await this.updateDailyNutritionSummary(meal.userId, meal.date);
  }

  // Daily nutrition summary operations
  async getDailyNutritionSummary(userId: string, date: string): Promise<DailyNutritionSummary | undefined> {
    const key = `${userId}-${date}`;
    return this.dailyNutritionSummaries.get(key);
  }

  async updateDailyNutritionSummary(userId: string, date: string): Promise<DailyNutritionSummary> {
    const key = `${userId}-${date}`;
    const meals = await this.getMealsByDate(userId, date);
    
    const totals = meals.reduce((acc, meal) => ({
      calories: acc.calories + Number(meal.totalCalories),
      protein: acc.protein + Number(meal.totalProtein),
      carbs: acc.carbs + Number(meal.totalCarbs),
      fat: acc.fat + Number(meal.totalFat),
      fiber: acc.fiber,
      sodium: acc.sodium,
    }), { calories: 0, protein: 0, carbs: 0, fat: 0, fiber: 0, sodium: 0 });
    
    // Get user profile for goals
    const profile = await this.getUserProfile(userId);
    
    const now = new Date();
    const summary: DailyNutritionSummary = {
      id: randomUUID(),
      userId,
      date,
      totalCalories: String(totals.calories.toFixed(2)),
      totalProtein: String(totals.protein.toFixed(2)),
      totalCarbs: String(totals.carbs.toFixed(2)),
      totalFat: String(totals.fat.toFixed(2)),
      totalFiber: String(totals.fiber.toFixed(2)),
      totalSodium: String(totals.sodium.toFixed(2)),
      caloriesGoal: profile?.calorieTarget ? String(profile.calorieTarget) : null,
      proteinGoal: profile?.proteinTarget ? String(profile.proteinTarget) : null,
      carbsGoal: profile?.carbTarget ? String(profile.carbTarget) : null,
      fatGoal: profile?.fatTarget ? String(profile.fatTarget) : null,
      createdAt: now,
      updatedAt: now,
    };
    
    this.dailyNutritionSummaries.set(key, summary);
    return summary;
  }

  async getNutritionSummaryRange(userId: string, startDate: string, endDate: string): Promise<DailyNutritionSummary[]> {
    return Array.from(this.dailyNutritionSummaries.values()).filter(summary => 
      summary.userId === userId && 
      summary.date >= startDate && 
      summary.date <= endDate
    ).sort((a, b) => a.date.localeCompare(b.date));
  }

  // Meal plan operations
  async getUserMealPlans(userId: string): Promise<MealPlan[]> {
    return Array.from(this.mealPlans.values()).filter(plan => 
      plan.userId === userId
    ).sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }

  async getMealPlan(id: string): Promise<MealPlan | undefined> {
    return this.mealPlans.get(id);
  }

  async createMealPlan(insertPlan: InsertMealPlan): Promise<MealPlan> {
    const id = randomUUID();
    const now = new Date();
    const plan: MealPlan = {
      ...insertPlan,
      id,
      targetCalories: insertPlan.targetCalories ? String(insertPlan.targetCalories) : null,
      targetProtein: insertPlan.targetProtein ? String(insertPlan.targetProtein) : null,
      targetCarbs: insertPlan.targetCarbs ? String(insertPlan.targetCarbs) : null,
      targetFat: insertPlan.targetFat ? String(insertPlan.targetFat) : null,
      createdAt: now,
      updatedAt: now,
    };
    this.mealPlans.set(id, plan);
    return plan;
  }

  async deleteMealPlan(id: string): Promise<boolean> {
    // Also delete associated meal plan items
    const items = Array.from(this.mealPlanItems.values()).filter(item => item.mealPlanId === id);
    items.forEach(item => this.mealPlanItems.delete(item.id));
    
    return this.mealPlans.delete(id);
  }

  // Meal plan item operations
  async getMealPlanItems(mealPlanId: string): Promise<MealPlanItem[]> {
    return Array.from(this.mealPlanItems.values()).filter(item => 
      item.mealPlanId === mealPlanId
    ).sort((a, b) => {
      const mealOrder = { breakfast: 1, lunch: 2, dinner: 3, snack: 4 };
      const mealComparison = (mealOrder[a.mealType as keyof typeof mealOrder] || 5) - 
                            (mealOrder[b.mealType as keyof typeof mealOrder] || 5);
      return mealComparison !== 0 ? mealComparison : a.orderInMeal - b.orderInMeal;
    });
  }

  async addMealPlanItem(insertItem: InsertMealPlanItem): Promise<MealPlanItem> {
    const id = randomUUID();
    const food = this.foods.get(insertItem.foodId);
    if (!food) throw new Error('Food not found');
    
    const grams = Number(insertItem.grams);
    const nutrition = this.calculateNutritionFromGrams(food, grams);
    
    const item: MealPlanItem = {
      ...insertItem,
      id,
      grams: String(grams),
      calories: String(nutrition.calories.toFixed(2)),
      protein: String(nutrition.protein.toFixed(2)),
      carbs: String(nutrition.carbs.toFixed(2)),
      fat: String(nutrition.fat.toFixed(2)),
    };
    
    this.mealPlanItems.set(id, item);
    return item;
  }

  async deleteMealPlanItem(id: string): Promise<boolean> {
    return this.mealPlanItems.delete(id);
  }

  async applyMealPlanToDate(mealPlanId: string, userId: string, date: string): Promise<Meal[]> {
    const planItems = await this.getMealPlanItems(mealPlanId);
    const createdMeals: Meal[] = [];
    
    // Group plan items by meal type
    const mealGroups = planItems.reduce((acc, item) => {
      if (!acc[item.mealType]) acc[item.mealType] = [];
      acc[item.mealType].push(item);
      return acc;
    }, {} as Record<string, MealPlanItem[]>);
    
    // Create meals for each meal type
    for (const [mealType, items] of Object.entries(mealGroups)) {
      const meal = await this.createMeal({
        userId,
        date,
        mealType: mealType as any,
        name: `Planned ${mealType}`,
        notes: `Applied from meal plan`,
      });
      
      // Add items to the meal
      for (const item of items) {
        await this.addMealItem({
          mealId: meal.id,
          foodId: item.foodId,
          grams: Number(item.grams),
        });
      }
      
      createdMeals.push(meal);
    }
    
    return createdMeals;
  }
}

export const storage = new MemStorage();
