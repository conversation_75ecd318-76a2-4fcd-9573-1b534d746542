# Circuit - AI-Powered Fitness Coaching Platform

> **Coach-led • AI-assisted** - The future of personalized fitness training

Circuit is a comprehensive fitness coaching web application that combines human expertise with cutting-edge AI technology. Built with modern web technologies, it provides personalized workout tracking, nutrition planning, progress monitoring, and real-time form analysis through computer vision.

## 🚀 Features Overview

### 🎯 Computer Vision Form Analysis
- **Real-time Exercise Form Assessment**: AI-powered analysis of workout form using OpenAI's GPT-4 Vision API
- **Live Camera Integration**: Capture exercise videos and photos directly in the browser
- **Instant Feedback**: Get immediate coaching tips and form corrections
- **Progress Photo Comparison**: Track physical transformation over time
- **Exercise Recognition**: Automatically identify exercises and provide targeted advice

### 💪 Comprehensive Workout Tracking
- **Session Management**: Track complete workout sessions with timestamps
- **Exercise Logging**: Log sets, reps, weights, and rest periods
- **Built-in Rest Timer**: Automated rest timers between sets
- **Exercise Library**: Pre-loaded with common exercises and equipment requirements
- **Personal Records**: Automatic tracking of PRs and strength progression
- **Workout History**: Complete history of all training sessions

### 📊 Advanced Analytics Dashboard
- **Progress Visualization**: Interactive charts showing strength and performance trends
- **Volume Tracking**: Monitor training volume over time with weekly/monthly views
- **Goal Progress**: Visual tracking of fitness goals with completion percentages
- **Personal Records Timeline**: Track PR achievements across different exercises
- **Nutrition Integration**: Combined fitness and nutrition analytics in one view
- **Export Data**: Download workout and nutrition data for external analysis

### 🤖 AI Coaching Assistant
- **Multi-Persona Coaching**: Choose from 4 distinct coaching styles:
  - **Motivational Coach**: High-energy motivation and encouragement
  - **Technical Coach**: Detailed form analysis and technique guidance
  - **Supportive Coach**: Gentle guidance and emotional support
  - **Personal Trainer**: Structured programming and goal-oriented advice
- **Context-Aware Conversations**: AI understands your current workout, goals, and progress
- **Quick Actions**: Instant access to workout tips, form checks, and motivation
- **Session Integration**: Chat seamlessly integrated across all app sections
- **Rate Limited**: Secure and controlled AI interactions to prevent abuse

### 🍽️ Comprehensive Nutrition System
- **Food Database**: Extensive database of foods with complete nutritional information
- **Meal Logging**: Easy meal tracking with portion control and macro counting
- **AI Meal Planning**: Personalized meal plans generated based on:
  - Fitness goals and caloric needs
  - Dietary restrictions and preferences
  - Preferred cuisines and cooking styles
  - Current activity level and workout schedule
- **Macro Tracking**: Monitor protein, carbs, fats, fiber, and sodium intake
- **Daily Summaries**: Real-time nutrition progress vs. daily goals
- **Nutrition Analytics**: Weekly trends and macro distribution charts

### 👤 Intelligent User Profiles
- **Goal Setting**: Define specific fitness objectives (weight loss, muscle gain, endurance)
- **Fitness Assessment**: Track current fitness level and experience
- **Equipment Management**: Specify available equipment for personalized workouts
- **Dietary Preferences**: Set restrictions, allergies, and cuisine preferences
- **Progress Tracking**: Monitor goal completion and milestone achievements
- **Personalized Recommendations**: AI-driven suggestions based on profile data

## 🛠️ Technical Architecture

### Frontend Technologies
- **React 18+** with TypeScript for type-safe development
- **Vite** for fast build tooling and hot module replacement
- **Wouter** for lightweight client-side routing
- **shadcn/ui** component library built on Radix UI primitives
- **Tailwind CSS** with custom design tokens and dark/light theme support
- **TanStack Query** for intelligent server state management and caching
- **React Hook Form** with Zod validation for robust form handling
- **Recharts** for interactive data visualization and analytics

### Backend Infrastructure
- **Node.js** with Express.js server framework
- **TypeScript** throughout with ES modules
- **Drizzle ORM** for type-safe database operations
- **PostgreSQL** database with comprehensive schemas
- **Rate Limiting** middleware for API protection
- **Zod** validation for runtime type checking
- **Session-based** user management

### AI & Machine Learning Integration
- **OpenAI GPT-4o-mini** for conversational AI coaching
- **OpenAI Vision API** for exercise form analysis and progress photos
- **Structured AI Responses** with JSON validation and error handling
- **Context-Aware Prompting** for personalized coaching experiences
- **Rate Limiting** and cost control for AI services

### Security & Production Features
- **Input Validation** with comprehensive Zod schemas
- **Rate Limiting** on all AI endpoints to prevent abuse
- **Request Size Limits** for file uploads (10MB maximum)
- **Error Handling** with user-friendly messages and detailed logging
- **No Password Storage** - secure design without authentication vulnerabilities
- **API Key Protection** - secure server-side AI service integration

## 🎨 Design & User Experience

### Modern Design System
- **Consistent Color Palette** with Circuit's signature red branding
- **Dark/Light Theme** support with automatic system preference detection
- **Responsive Design** optimized for desktop, tablet, and mobile devices
- **Accessibility First** with proper ARIA labels and keyboard navigation
- **Professional Aesthetic** matching modern fitness app standards

### User Interface Features
- **Intuitive Navigation** with clear information architecture
- **Real-time Updates** with optimistic UI updates and caching
- **Loading States** and skeleton screens for smooth user experience
- **Error Recovery** with helpful messages and retry mechanisms
- **Offline Indicators** for network status awareness

## 📱 Key User Workflows

### 1. Workout Session Flow
1. Start a new workout session
2. Log exercises with sets, reps, and weights
3. Use camera for form analysis and feedback
4. Get AI coaching tips during rest periods
5. Complete session with automatic PR tracking

### 2. Nutrition Planning Flow
1. Set nutrition goals in user profile
2. Generate AI meal plan based on preferences
3. Log daily meals with macro tracking
4. Monitor progress against daily targets
5. Adjust goals based on performance data

### 3. Progress Analysis Flow
1. View analytics dashboard with progress charts
2. Analyze workout volume and strength trends
3. Review nutrition adherence and patterns
4. Set new goals based on data insights
5. Get AI recommendations for improvements

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ 
- PostgreSQL database
- OpenAI API key

### Environment Setup
```bash
# Required environment variables
DATABASE_URL=your_postgres_connection_string
OPENAI_API_KEY=your_openai_api_key
SESSION_SECRET=your_session_secret
```

### Installation
```bash
# Install dependencies
npm install

# Start development server
npm run dev
```

The application will be available at `http://localhost:5000` with both frontend and backend running on the same port.

## 🔧 Development Features

### Code Quality
- **TypeScript** throughout for type safety
- **ESLint** and **Prettier** for code consistency
- **Hot Module Replacement** for fast development cycles
- **Comprehensive Test IDs** for automated testing support
- **Modular Architecture** with clear separation of concerns

### Database Management
- **Type-safe Schemas** with Drizzle ORM
- **Automatic Migrations** with schema synchronization
- **In-memory Development** storage with easy PostgreSQL migration
- **Data Validation** at both client and server levels

### AI Integration Best Practices
- **Conversation Windowing** to manage token usage and costs
- **Structured Responses** with Zod validation for reliability
- **Fallback Mechanisms** for AI service failures
- **Usage Monitoring** and rate limiting for cost control

## 📊 Analytics & Insights

Circuit provides comprehensive analytics across all aspects of fitness and nutrition:

- **Workout Performance**: Track strength progression, volume trends, and training consistency
- **Nutrition Adherence**: Monitor macro targets, calorie goals, and meal plan compliance
- **Goal Achievement**: Visualize progress toward fitness and nutrition objectives
- **AI Interaction**: Analyze coaching effectiveness and user engagement patterns

## 🔒 Security & Privacy

- **No Password Storage** - eliminates common authentication vulnerabilities
- **Rate Limited APIs** - prevents abuse and controls costs
- **Input Validation** - comprehensive server-side validation of all user inputs
- **Secure AI Integration** - API keys protected server-side, no client exposure
- **Request Size Limits** - prevents malicious file uploads and DoS attacks

## 🌟 What Makes Circuit Special

1. **Holistic Approach**: Combines workout tracking, nutrition planning, and AI coaching in one platform
2. **Computer Vision Innovation**: Real-time form analysis using state-of-the-art AI vision technology  
3. **Personalized AI Coaching**: Context-aware coaching that understands your complete fitness journey
4. **Production Ready**: Built with enterprise-grade security, error handling, and performance optimization
5. **Modern Tech Stack**: Leverages the latest web technologies for optimal performance and developer experience

## 🎯 Future Roadmap

Circuit is designed as a comprehensive fitness coaching platform with room for enhancement:

- **Authentication System**: User accounts with secure login and data persistence
- **Social Features**: Share progress, compete with friends, community challenges
- **Wearable Integration**: Connect with fitness trackers and smartwatches
- **Video Workouts**: Integrated workout videos with AI form analysis
- **Meal Prep Planning**: Advanced meal planning with shopping lists and prep schedules
- **Advanced Analytics**: Predictive insights and personalized recommendations

---

**Built with ❤️ by the Circuit Team**

*Empowering fitness journeys through intelligent technology and personalized coaching.*