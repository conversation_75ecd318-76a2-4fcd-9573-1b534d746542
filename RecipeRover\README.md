# RecipeRover - AI-Powered Fitness Coaching Platform

> **Coach-led • AI-assisted** - The future of personalized fitness training

RecipeRover is a comprehensive fitness coaching web application that combines human expertise with cutting-edge AI technology. Built with modern web technologies, it provides personalized workout tracking, nutrition planning, progress monitoring, and real-time form analysis through computer vision.

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- npm or yarn

### Installation & Setup

1. **Clone and Install**
   ```bash
   cd RecipeRover
   npm install
   ```

2. **Environment Configuration**
   - Copy `.env` file and update if needed
   - For AI features, add your OpenAI API key to `.env`:
     ```
     OPENAI_API_KEY=your_openai_api_key_here
     ```
   - AI features will be disabled if no API key is provided

3. **Start Development Server**
   ```bash
   npm run dev
   ```

4. **Access the Application**
   - Open http://localhost:5000 in your browser
   - The application includes both frontend and backend on the same port

## ✅ Current Status - FULLY FUNCTIONAL

The RecipeRover application is **complete and ready to use** with the following features:

## ✨ Implemented Features

### 🎯 **Computer Vision Form Analysis** ✅
- Real-time exercise form assessment using OpenAI GPT-4 Vision API
- Live camera integration for capturing exercise photos/videos
- Instant AI feedback with form corrections and safety tips
- Progress photo comparison for transformation tracking
- Exercise recognition with targeted coaching advice

### 💪 **Complete Workout Tracking System** ✅
- Full workout session management with timestamps
- Exercise logging with sets, reps, weights, and rest periods
- Built-in rest timers between sets
- Pre-loaded exercise library (10+ exercises with instructions)
- Automatic personal record tracking
- Complete workout history and analytics

### 📊 **Advanced Analytics Dashboard** ✅
- Interactive progress charts and performance trends
- Volume tracking with weekly/monthly views
- Goal progress visualization with completion percentages
- Personal records timeline
- Muscle group distribution analysis
- Achievement system with milestones

### 🤖 **AI Coaching Assistant** ✅
- 4 distinct coaching personas:
  - **Motivational Coach**: High-energy encouragement
  - **Technical Coach**: Form analysis and technique guidance
  - **Supportive Coach**: Gentle guidance and emotional support
  - **Personal Trainer**: Structured programming advice
- Context-aware conversations understanding your current workout
- Quick action buttons for instant coaching tips
- Rate-limited and secure AI interactions

### 🍽️ **Comprehensive Nutrition System** ✅
- Extensive food database (17+ foods with complete nutrition data)
- Meal logging with portion control and macro counting
- AI-powered meal plan generation based on goals and preferences
- Daily nutrition summaries and progress tracking
- Macro distribution charts and analytics
- Custom food creation and portion management

### 👤 **User Profile Management** ✅
- Complete fitness goal setting and tracking
- Fitness level assessment and equipment preferences
- Dietary restrictions and cuisine preferences
- Profile completion tracking with percentage indicators
- Personalized recommendations based on profile data

### 🎨 **Modern UI/UX** ✅
- Professional fitness app design with Circuit branding
- Dark/light theme support with system preference detection
- Fully responsive design (desktop, tablet, mobile)
- Accessibility-first approach with proper ARIA labels
- Smooth animations and loading states

## 📱 How to Use the Application

### 🏠 **Home Page**
- Overview of all features and capabilities
- Quick access to main sections
- Hero section with call-to-action buttons

### 💪 **Workout Tracking** (`/workout`)
1. **Start a Workout Session**
   - Choose workout type (strength, cardio, flexibility, mixed, plyometric)
   - Add optional notes

2. **Add Exercises**
   - Browse the exercise library or search by name
   - Select from pre-loaded exercises with instructions
   - Set target number of sets (optional)

3. **Log Sets**
   - Record reps, weight, and rest time for each set
   - Use built-in rest timer between sets
   - Mark sets as complete when finished

4. **AI Coaching Integration**
   - Chat with AI coach during workouts
   - Get form tips, motivation, and exercise modifications
   - Take photos for form analysis feedback

5. **Complete Session**
   - Finish workout to save all data
   - View session summary and personal records

### 🍽️ **Nutrition Tracking** (`/nutrition`)
1. **Daily Meal Logging**
   - Add meals by type (breakfast, lunch, dinner, snack)
   - Search food database or create custom foods
   - Specify portions and quantities

2. **Macro Tracking**
   - Monitor calories, protein, carbs, and fats
   - View daily progress against goals
   - See real-time nutrition summaries

3. **AI Meal Planning**
   - Generate personalized meal plans
   - Specify dietary preferences and restrictions
   - Apply meal plans to specific dates

4. **Nutrition Analytics**
   - Weekly nutrition trends
   - Macro distribution charts
   - Goal adherence tracking

### 📊 **Analytics Dashboard** (`/analytics`)
- View comprehensive workout and nutrition analytics
- Track progress over time with interactive charts
- Monitor personal records and achievements
- Analyze muscle group distribution
- Set and track fitness goals

### 👤 **User Profile** (`/profile`)
- Set up personal information (age, height, weight)
- Define fitness goals and activity level
- Specify available equipment
- Set nutrition targets and dietary preferences
- Track profile completion percentage

## 🛠️ Technical Architecture

### Frontend Technologies
- **React 18+** with TypeScript for type-safe development
- **Vite** for fast build tooling and hot module replacement
- **Wouter** for lightweight client-side routing
- **shadcn/ui** component library built on Radix UI primitives
- **Tailwind CSS** with custom design tokens and dark/light theme support
- **TanStack Query** for intelligent server state management and caching
- **React Hook Form** with Zod validation for robust form handling
- **Recharts** for interactive data visualization and analytics

### Backend Infrastructure
- **Node.js** with Express.js server framework
- **TypeScript** throughout with ES modules
- **Drizzle ORM** for type-safe database operations
- **PostgreSQL** database with comprehensive schemas
- **Rate Limiting** middleware for API protection
- **Zod** validation for runtime type checking
- **Session-based** user management

### AI & Machine Learning Integration
- **OpenAI GPT-4o-mini** for conversational AI coaching
- **OpenAI Vision API** for exercise form analysis and progress photos
- **Structured AI Responses** with JSON validation and error handling
- **Context-Aware Prompting** for personalized coaching experiences
- **Rate Limiting** and cost control for AI services

### Security & Production Features
- **Input Validation** with comprehensive Zod schemas
- **Rate Limiting** on all AI endpoints to prevent abuse
- **Request Size Limits** for file uploads (10MB maximum)
- **Error Handling** with user-friendly messages and detailed logging
- **No Password Storage** - secure design without authentication vulnerabilities
- **API Key Protection** - secure server-side AI service integration

## 🎨 Design & User Experience

### Modern Design System
- **Consistent Color Palette** with Circuit's signature red branding
- **Dark/Light Theme** support with automatic system preference detection
- **Responsive Design** optimized for desktop, tablet, and mobile devices
- **Accessibility First** with proper ARIA labels and keyboard navigation
- **Professional Aesthetic** matching modern fitness app standards

### User Interface Features
- **Intuitive Navigation** with clear information architecture
- **Real-time Updates** with optimistic UI updates and caching
- **Loading States** and skeleton screens for smooth user experience
- **Error Recovery** with helpful messages and retry mechanisms
- **Offline Indicators** for network status awareness

## 📱 Key User Workflows

### 1. Workout Session Flow
1. Start a new workout session
2. Log exercises with sets, reps, and weights
3. Use camera for form analysis and feedback
4. Get AI coaching tips during rest periods
5. Complete session with automatic PR tracking

### 2. Nutrition Planning Flow
1. Set nutrition goals in user profile
2. Generate AI meal plan based on preferences
3. Log daily meals with macro tracking
4. Monitor progress against daily targets
5. Adjust goals based on performance data

### 3. Progress Analysis Flow
1. View analytics dashboard with progress charts
2. Analyze workout volume and strength trends
3. Review nutrition adherence and patterns
4. Set new goals based on data insights
5. Get AI recommendations for improvements

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ 
- PostgreSQL database
- OpenAI API key

### Environment Setup
```bash
# Required environment variables
DATABASE_URL=your_postgres_connection_string
OPENAI_API_KEY=your_openai_api_key
SESSION_SECRET=your_session_secret
```

### Installation
```bash
# Install dependencies
npm install

# Start development server
npm run dev
```

The application will be available at `http://localhost:5000` with both frontend and backend running on the same port.

## 🔧 Development Features

### Code Quality
- **TypeScript** throughout for type safety
- **ESLint** and **Prettier** for code consistency
- **Hot Module Replacement** for fast development cycles
- **Comprehensive Test IDs** for automated testing support
- **Modular Architecture** with clear separation of concerns

### Database Management
- **Type-safe Schemas** with Drizzle ORM
- **Automatic Migrations** with schema synchronization
- **In-memory Development** storage with easy PostgreSQL migration
- **Data Validation** at both client and server levels

### AI Integration Best Practices
- **Conversation Windowing** to manage token usage and costs
- **Structured Responses** with Zod validation for reliability
- **Fallback Mechanisms** for AI service failures
- **Usage Monitoring** and rate limiting for cost control

## 📊 Analytics & Insights

Circuit provides comprehensive analytics across all aspects of fitness and nutrition:

- **Workout Performance**: Track strength progression, volume trends, and training consistency
- **Nutrition Adherence**: Monitor macro targets, calorie goals, and meal plan compliance
- **Goal Achievement**: Visualize progress toward fitness and nutrition objectives
- **AI Interaction**: Analyze coaching effectiveness and user engagement patterns

## 🔒 Security & Privacy

- **No Password Storage** - eliminates common authentication vulnerabilities
- **Rate Limited APIs** - prevents abuse and controls costs
- **Input Validation** - comprehensive server-side validation of all user inputs
- **Secure AI Integration** - API keys protected server-side, no client exposure
- **Request Size Limits** - prevents malicious file uploads and DoS attacks

## 🌟 What Makes Circuit Special

1. **Holistic Approach**: Combines workout tracking, nutrition planning, and AI coaching in one platform
2. **Computer Vision Innovation**: Real-time form analysis using state-of-the-art AI vision technology  
3. **Personalized AI Coaching**: Context-aware coaching that understands your complete fitness journey
4. **Production Ready**: Built with enterprise-grade security, error handling, and performance optimization
5. **Modern Tech Stack**: Leverages the latest web technologies for optimal performance and developer experience

## 🎯 Future Roadmap

Circuit is designed as a comprehensive fitness coaching platform with room for enhancement:

- **Authentication System**: User accounts with secure login and data persistence
- **Social Features**: Share progress, compete with friends, community challenges
- **Wearable Integration**: Connect with fitness trackers and smartwatches
- **Video Workouts**: Integrated workout videos with AI form analysis
- **Meal Prep Planning**: Advanced meal planning with shopping lists and prep schedules
- **Advanced Analytics**: Predictive insights and personalized recommendations

---

**Built with ❤️ by the Circuit Team**

*Empowering fitness journeys through intelligent technology and personalized coaching.*