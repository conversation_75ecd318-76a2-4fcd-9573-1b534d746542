# RecipeRover Deployment Guide

## 🚀 Production Deployment

### Prerequisites
- Node.js 18+ 
- npm or yarn
- (Optional) PostgreSQL database for persistence
- (Optional) OpenAI API key for AI features

### Environment Configuration

1. **Copy and configure environment variables:**
   ```bash
   cp .env .env.production
   ```

2. **Update .env.production with production values:**
   ```env
   # OpenAI API Configuration (Required for AI features)
   OPENAI_API_KEY=your_production_openai_api_key

   # Session Configuration (IMPORTANT: Change this!)
   SESSION_SECRET=your_very_long_random_session_secret_for_production

   # Server Configuration
   PORT=5000
   NODE_ENV=production

   # Database Configuration (Optional - uses in-memory storage if not provided)
   # DATABASE_URL=postgresql://username:password@localhost:5432/recipeRover
   ```

### Build and Deploy

1. **Install dependencies:**
   ```bash
   npm install --production
   ```

2. **Build the application:**
   ```bash
   npm run build
   ```

3. **Start the production server:**
   ```bash
   npm start
   ```

### Docker Deployment (Optional)

Create a `Dockerfile`:
```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm install --production

COPY . .
RUN npm run build

EXPOSE 5000

CMD ["npm", "start"]
```

Build and run:
```bash
docker build -t reciperover .
docker run -p 5000:5000 --env-file .env.production reciperover
```

### Health Checks

The application provides several endpoints for monitoring:

- **Health Check:** `GET /api/health` (if implemented)
- **Exercise Library:** `GET /api/exercises` (should return 10+ exercises)
- **Food Database:** `GET /api/nutrition/foods` (should return 17+ foods)

### Performance Considerations

1. **In-Memory Storage:** Current implementation uses in-memory storage which is fast but not persistent across restarts
2. **Database Migration:** For production, consider implementing PostgreSQL with the existing schema
3. **AI Rate Limiting:** OpenAI API calls are rate-limited to prevent abuse
4. **Session Management:** Uses secure session management with configurable secrets

### Security Notes

- Change the default SESSION_SECRET in production
- Keep OpenAI API key secure and never commit to version control
- Consider implementing user authentication for multi-user deployments
- The application currently uses a mock user system suitable for single-user or demo purposes

### Monitoring

Monitor these key metrics:
- API response times
- Memory usage (important for in-memory storage)
- OpenAI API usage and costs
- User session activity

### Backup Strategy

Since the current implementation uses in-memory storage:
- Data is lost on server restart
- Consider implementing periodic data exports
- For production, migrate to persistent database storage

## 🔧 Development vs Production

| Feature | Development | Production |
|---------|-------------|------------|
| Storage | In-memory | In-memory (or PostgreSQL) |
| AI Features | Optional | Recommended |
| Session Secret | Default | Custom secure secret |
| Error Handling | Detailed | User-friendly |
| Logging | Console | File/Service |
