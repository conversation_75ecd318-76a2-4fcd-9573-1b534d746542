import { sql } from "drizzle-orm";
import { pgTable, text, varchar, integer, timestamp, decimal, boolean } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

export const users = pgTable("users", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  username: text("username").notNull().unique(),
});

export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
});

export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;

export const userProfiles = pgTable("user_profiles", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  userId: varchar("user_id").notNull().unique(), // Reference to user
  
  // Personal Information
  age: integer("age"),
  height: decimal("height", { precision: 5, scale: 2 }), // Height in cm
  weight: decimal("weight", { precision: 5, scale: 2 }), // Weight in kg
  fitnessLevel: text("fitness_level").notNull().default("beginner"), // beginner, intermediate, advanced
  
  // Goals (array of selected goals)
  goals: text("goals").array().notNull().default([]), // weight_loss, muscle_gain, strength, endurance, general_fitness
  targetWeight: decimal("target_weight", { precision: 5, scale: 2 }), // Optional target weight in kg
  targetDate: timestamp("target_date"), // Optional target date for goals
  
  // Preferences
  preferredWorkoutDuration: integer("preferred_workout_duration").notNull().default(45), // Duration in minutes
  weeklyWorkoutFrequency: integer("weekly_workout_frequency").notNull().default(3), // Times per week
  availableEquipment: text("available_equipment").array().notNull().default([]), // Equipment user has access to
  
  // Activity Level
  activityLevel: text("activity_level").notNull().default("lightly_active"), // sedentary, lightly_active, moderately_active, very_active
  
  // Nutrition Goals and Preferences
  calorieTarget: integer("calorie_target"), // Daily calorie target
  proteinTarget: decimal("protein_target", { precision: 5, scale: 2 }), // Daily protein target in grams
  carbTarget: decimal("carb_target", { precision: 5, scale: 2 }), // Daily carb target in grams
  fatTarget: decimal("fat_target", { precision: 5, scale: 2 }), // Daily fat target in grams
  dietType: text("diet_type"), // general, vegetarian, vegan, keto, paleo, mediterranean, low_carb
  restrictions: text("restrictions").array().notNull().default([]), // allergies, dislikes, dietary restrictions
  preferredCuisines: text("preferred_cuisines").array().notNull().default([]), // preferred food types/cuisines
  
  // Profile completion tracking
  isComplete: boolean("is_complete").notNull().default(false),
  
  // Timestamps
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

export const insertUserProfileSchema = createInsertSchema(userProfiles).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
}).extend({
  age: z.number().min(13).max(120).optional(),
  height: z.coerce.number().min(100).max(250).optional(), // Height in cm
  weight: z.coerce.number().min(30).max(300).optional(), // Weight in kg
  fitnessLevel: z.enum(["beginner", "intermediate", "advanced"]),
  goals: z.array(z.enum(["weight_loss", "muscle_gain", "strength", "endurance", "general_fitness"])).min(1),
  targetWeight: z.coerce.number().min(30).max(300).optional(),
  targetDate: z.date().optional(),
  preferredWorkoutDuration: z.number().min(15).max(180).default(45),
  weeklyWorkoutFrequency: z.number().min(1).max(7).default(3),
  availableEquipment: z.array(z.string()).default([]),
  activityLevel: z.enum(["sedentary", "lightly_active", "moderately_active", "very_active"]),
  calorieTarget: z.number().min(800).max(5000).optional(),
  proteinTarget: z.coerce.number().min(20).max(300).optional(),
  carbTarget: z.coerce.number().min(50).max(600).optional(),
  fatTarget: z.coerce.number().min(20).max(200).optional(),
  dietType: z.enum(["general", "vegetarian", "vegan", "keto", "paleo", "mediterranean", "low_carb"]).optional(),
  restrictions: z.array(z.string()).default([]),
  preferredCuisines: z.array(z.string()).default([]),
});

export const updateUserProfileSchema = createInsertSchema(userProfiles).omit({
  id: true,
  userId: true,
  createdAt: true,
  updatedAt: true,
}).extend({
  age: z.number().min(13).max(120).optional(),
  height: z.coerce.number().min(100).max(250).optional(),
  weight: z.coerce.number().min(30).max(300).optional(),
  fitnessLevel: z.enum(["beginner", "intermediate", "advanced"]).optional(),
  goals: z.array(z.enum(["weight_loss", "muscle_gain", "strength", "endurance", "general_fitness"])).optional(),
  targetWeight: z.coerce.number().min(30).max(300).optional(),
  targetDate: z.date().optional(),
  preferredWorkoutDuration: z.number().min(15).max(180).optional(),
  weeklyWorkoutFrequency: z.number().min(1).max(7).optional(),
  availableEquipment: z.array(z.string()).optional(),
  activityLevel: z.enum(["sedentary", "lightly_active", "moderately_active", "very_active"]).optional(),
  calorieTarget: z.number().min(800).max(5000).optional(),
  proteinTarget: z.coerce.number().min(20).max(300).optional(),
  carbTarget: z.coerce.number().min(50).max(600).optional(),
  fatTarget: z.coerce.number().min(20).max(200).optional(),
  dietType: z.enum(["general", "vegetarian", "vegan", "keto", "paleo", "mediterranean", "low_carb"]).optional(),
  restrictions: z.array(z.string()).optional(),
  preferredCuisines: z.array(z.string()).optional(),
}).partial();

export type InsertUserProfile = z.infer<typeof insertUserProfileSchema>;
export type UpdateUserProfile = z.infer<typeof updateUserProfileSchema>;
export type UserProfile = typeof userProfiles.$inferSelect;

export const exercises = pgTable("exercises", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  name: text("name").notNull(),
  description: text("description").notNull(),
  instructions: text("instructions").array().notNull(), // Step-by-step instructions
  primaryMuscleGroups: text("primary_muscle_groups").array().notNull(), // Primary muscles targeted
  secondaryMuscleGroups: text("secondary_muscle_groups").array().default([]), // Secondary muscles
  difficulty: text("difficulty").notNull(), // beginner, intermediate, advanced
  equipment: text("equipment").array().notNull(), // Equipment needed (e.g., ["dumbbell", "bench"] or ["bodyweight"])
  category: text("category").notNull(), // strength, cardio, flexibility, etc.
  safetyTips: text("safety_tips").array().default([]), // Safety considerations
  videoUrl: text("video_url"), // Optional video reference
  imageUrl: text("image_url"), // Optional image reference
});

export const insertExerciseSchema = createInsertSchema(exercises).omit({
  id: true,
}).extend({
  difficulty: z.enum(["beginner", "intermediate", "advanced"]),
  category: z.enum(["strength", "cardio", "flexibility", "mobility", "plyometric"]),
  primaryMuscleGroups: z.array(z.enum([
    "chest", "back", "shoulders", "biceps", "triceps", "forearms",
    "abs", "obliques", "lower back", "quadriceps", "hamstrings", 
    "glutes", "calves", "hip flexors", "full body"
  ])).min(1),
  secondaryMuscleGroups: z.array(z.enum([
    "chest", "back", "shoulders", "biceps", "triceps", "forearms",
    "abs", "obliques", "lower back", "quadriceps", "hamstrings", 
    "glutes", "calves", "hip flexors", "full body"
  ])).optional(),
  equipment: z.array(z.string()).min(1),
  instructions: z.array(z.string()).min(1),
});

export type InsertExercise = z.infer<typeof insertExerciseSchema>;
export type Exercise = typeof exercises.$inferSelect;

export const workoutSessions = pgTable("workout_sessions", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  userId: varchar("user_id").notNull(), // Reference to user (not enforced FK in MemStorage)
  startedAt: timestamp("started_at").notNull().defaultNow(),
  completedAt: timestamp("completed_at"),
  duration: integer("duration"), // Duration in seconds
  workoutType: text("workout_type").notNull(), // e.g., "strength", "cardio", "mixed"
  notes: text("notes"),
  isCompleted: boolean("is_completed").notNull().default(false),
});

export const insertWorkoutSessionSchema = createInsertSchema(workoutSessions).omit({
  id: true,
  startedAt: true,
}).extend({
  workoutType: z.enum(["strength", "cardio", "flexibility", "mixed", "plyometric"]),
});

export type InsertWorkoutSession = z.infer<typeof insertWorkoutSessionSchema>;
export type WorkoutSession = typeof workoutSessions.$inferSelect;

export const sessionExercises = pgTable("session_exercises", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  sessionId: varchar("session_id").notNull(), // Reference to workout session
  exerciseName: text("exercise_name").notNull(), // Simple text input, not FK to exercises table
  orderInSession: integer("order_in_session").notNull().default(0),
  targetSets: integer("target_sets"),
  completedSets: integer("completed_sets").notNull().default(0),
  notes: text("notes"),
});

export const insertSessionExerciseSchema = createInsertSchema(sessionExercises).omit({
  id: true,
});

export type InsertSessionExercise = z.infer<typeof insertSessionExerciseSchema>;
export type SessionExercise = typeof sessionExercises.$inferSelect;

export const exerciseSets = pgTable("exercise_sets", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  sessionExerciseId: varchar("session_exercise_id").notNull(), // Reference to session exercise
  setNumber: integer("set_number").notNull(),
  reps: integer("reps"),
  weight: decimal("weight", { precision: 6, scale: 2 }), // Weight in kg/lbs with 2 decimal places
  restTime: integer("rest_time"), // Rest time in seconds
  isCompleted: boolean("is_completed").notNull().default(false),
  completedAt: timestamp("completed_at"),
  notes: text("notes"),
});

export const insertExerciseSetSchema = createInsertSchema(exerciseSets).omit({
  id: true,
  completedAt: true,
}).extend({
  weight: z.coerce.number().optional(),
  reps: z.coerce.number().optional(),
  restTime: z.coerce.number().optional(),
});

export type InsertExerciseSet = z.infer<typeof insertExerciseSetSchema>;
export type ExerciseSet = typeof exerciseSets.$inferSelect;

// Validation schemas for PATCH operations
export const updateWorkoutSessionSchema = z.object({
  notes: z.string().optional(),
  workoutType: z.enum(["strength", "cardio", "flexibility", "mixed", "plyometric"]).optional(),
}).strict();

export const updateSessionExerciseSchema = z.object({
  notes: z.string().optional(),
}).strict();

export type UpdateWorkoutSession = z.infer<typeof updateWorkoutSessionSchema>;
export type UpdateSessionExercise = z.infer<typeof updateSessionExerciseSchema>;

// Analytics types and schemas with JSON-safe transformations
export const dateRangeSchema = z.object({
  startDate: z.date(),
  endDate: z.date(),
});

export type DateRange = z.infer<typeof dateRangeSchema>;

// AI Coach Chat Tables
export const chatSessions = pgTable("chat_sessions", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  userId: varchar("user_id").notNull(), // Reference to user
  title: text("title").notNull().default("Coaching Session"), // Session title
  coachPersona: text("coach_persona").notNull().default("motivational"), // motivational, technical, supportive, personal_trainer
  context: text("context"), // JSON string with workout session, analysis results, etc.
  workoutSessionId: varchar("workout_session_id"), // Optional reference to current workout
  isActive: boolean("is_active").notNull().default(true),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

export const insertChatSessionSchema = createInsertSchema(chatSessions).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
}).extend({
  coachPersona: z.enum(["motivational", "technical", "supportive", "personal_trainer"]),
  context: z.string().optional(),
  workoutSessionId: z.string().optional(),
});

export const updateChatSessionSchema = createInsertSchema(chatSessions).omit({
  id: true,
  userId: true,
  createdAt: true,
  updatedAt: true,
}).extend({
  coachPersona: z.enum(["motivational", "technical", "supportive", "personal_trainer"]).optional(),
  context: z.string().optional(),
  workoutSessionId: z.string().optional(),
}).partial();

export type InsertChatSession = z.infer<typeof insertChatSessionSchema>;
export type UpdateChatSession = z.infer<typeof updateChatSessionSchema>;
export type ChatSession = typeof chatSessions.$inferSelect;

export const chatMessages = pgTable("chat_messages", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  sessionId: varchar("session_id").notNull(), // Reference to chat session
  sender: text("sender").notNull(), // "user" or "ai"
  content: text("content").notNull(),
  messageType: text("message_type").notNull().default("text"), // text, quick_action, system
  metadata: text("metadata"), // JSON string for additional data (e.g., workout suggestions, form analysis refs)
  createdAt: timestamp("created_at").notNull().defaultNow(),
});

export const insertChatMessageSchema = createInsertSchema(chatMessages).omit({
  id: true,
  createdAt: true,
}).extend({
  sender: z.enum(["user", "ai"]),
  messageType: z.enum(["text", "quick_action", "system"]),
  metadata: z.string().optional(),
});

export type InsertChatMessage = z.infer<typeof insertChatMessageSchema>;
export type ChatMessage = typeof chatMessages.$inferSelect;

export const analyticsOverviewSchema = z.object({
  totalWorkouts: z.number(),
  totalDuration: z.number(), // in minutes
  currentStreak: z.number(),
  longestStreak: z.number(),
  totalVolume: z.number(), // sum of (sets * reps * weight)
  personalRecords: z.number(),
  averageWorkoutDuration: z.number(), // in minutes
  completedGoals: z.number(),
  workoutConsistency: z.number(), // percentage
  favoriteExercise: z.string().optional(),
});

export type AnalyticsOverview = z.infer<typeof analyticsOverviewSchema>;

export const workoutTrendSchema = z.object({
  date: z.string(), // ISO date string for JSON safety
  workouts: z.number(),
  duration: z.number(), // in minutes
  volume: z.number(),
});

export type WorkoutTrend = z.infer<typeof workoutTrendSchema>;

export const volumeProgressionSchema = z.object({
  exercise: z.string(),
  date: z.string(), // ISO date string for JSON safety
  volume: z.number(),
  oneRepMax: z.number().optional(),
});

export type VolumeProgression = z.infer<typeof volumeProgressionSchema>;

export const personalRecordSchema = z.object({
  id: z.string(),
  exercise: z.string(),
  weight: z.number(),
  reps: z.number(),
  achievedAt: z.string(), // ISO date string for JSON safety
  previousBest: z.object({
    weight: z.number(),
    reps: z.number(),
    date: z.string(), // ISO date string for JSON safety
  }).optional(),
});

export type PersonalRecord = z.infer<typeof personalRecordSchema>;

export const muscleGroupDistributionSchema = z.object({
  muscleGroup: z.string(),
  sessions: z.number(),
  percentage: z.number(),
});

export type MuscleGroupDistribution = z.infer<typeof muscleGroupDistributionSchema>;

export const goalProgressSchema = z.object({
  goalType: z.string(),
  target: z.number(),
  current: z.number(),
  percentage: z.number(),
  onTrack: z.boolean(),
  estimatedCompletion: z.string().optional(), // ISO date string for JSON safety
});

export type GoalProgress = z.infer<typeof goalProgressSchema>;

export const achievementSchema = z.object({
  id: z.string(),
  type: z.enum(['streak', 'volume', 'pr', 'consistency', 'milestone']),
  title: z.string(),
  description: z.string(),
  achievedAt: z.string(), // ISO date string for JSON safety
  value: z.number(),
});

export type Achievement = z.infer<typeof achievementSchema>;

// Nutrition tracking tables

export const foods = pgTable("foods", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  name: text("name").notNull(),
  brand: text("brand"), // Optional brand name
  category: text("category").notNull(), // e.g., "protein", "vegetable", "grain", "dairy", "snack"
  
  // Nutritional data per 100g (canonical unit)
  caloriesPerHundredGrams: decimal("calories_per_hundred_grams", { precision: 6, scale: 2 }).notNull(),
  proteinPerHundredGrams: decimal("protein_per_hundred_grams", { precision: 6, scale: 2 }).notNull().default("0"),
  carbsPerHundredGrams: decimal("carbs_per_hundred_grams", { precision: 6, scale: 2 }).notNull().default("0"),
  fatPerHundredGrams: decimal("fat_per_hundred_grams", { precision: 6, scale: 2 }).notNull().default("0"),
  fiberPerHundredGrams: decimal("fiber_per_hundred_grams", { precision: 6, scale: 2 }).default("0"),
  sodiumPerHundredGrams: decimal("sodium_per_hundred_grams", { precision: 6, scale: 2 }).default("0"), // in mg
  
  // Metadata
  isUserCreated: boolean("is_user_created").notNull().default(false),
  createdBy: varchar("created_by"), // User ID who created this food
  barcode: text("barcode"), // For future barcode scanning
  description: text("description"),
  
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

export const insertFoodSchema = createInsertSchema(foods).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
}).extend({
  category: z.enum(["protein", "vegetable", "fruit", "grain", "dairy", "snack", "beverage", "condiment", "other"]),
  caloriesPerHundredGrams: z.coerce.number().min(0).max(1000),
  proteinPerHundredGrams: z.coerce.number().min(0).max(100).default(0),
  carbsPerHundredGrams: z.coerce.number().min(0).max(100).default(0),
  fatPerHundredGrams: z.coerce.number().min(0).max(100).default(0),
  fiberPerHundredGrams: z.coerce.number().min(0).max(100).default(0),
  sodiumPerHundredGrams: z.coerce.number().min(0).max(10000).default(0),
});

export type InsertFood = z.infer<typeof insertFoodSchema>;
export type Food = typeof foods.$inferSelect;

export const foodPortions = pgTable("food_portions", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  foodId: varchar("food_id").notNull(), // Reference to food
  name: text("name").notNull(), // e.g., "1 cup", "1 slice", "1 medium"
  gramsEquivalent: decimal("grams_equivalent", { precision: 8, scale: 2 }).notNull(), // How many grams this portion equals
  isDefault: boolean("is_default").notNull().default(false),
});

export const insertFoodPortionSchema = createInsertSchema(foodPortions).omit({
  id: true,
}).extend({
  gramsEquivalent: z.coerce.number().min(0.1).max(10000),
});

export type InsertFoodPortion = z.infer<typeof insertFoodPortionSchema>;
export type FoodPortion = typeof foodPortions.$inferSelect;

export const meals = pgTable("meals", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  userId: varchar("user_id").notNull(), // Reference to user
  date: text("date").notNull(), // YYYY-MM-DD format for easy querying
  mealType: text("meal_type").notNull(), // breakfast, lunch, dinner, snack
  name: text("name"), // Optional custom meal name
  notes: text("notes"),
  
  // Snapshot totals (calculated at log time for performance)
  totalCalories: decimal("total_calories", { precision: 8, scale: 2 }).notNull().default("0"),
  totalProtein: decimal("total_protein", { precision: 8, scale: 2 }).notNull().default("0"),
  totalCarbs: decimal("total_carbs", { precision: 8, scale: 2 }).notNull().default("0"),
  totalFat: decimal("total_fat", { precision: 8, scale: 2 }).notNull().default("0"),
  
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

export const insertMealSchema = createInsertSchema(meals).omit({
  id: true,
  totalCalories: true,
  totalProtein: true,
  totalCarbs: true,
  totalFat: true,
  createdAt: true,
  updatedAt: true,
}).extend({
  mealType: z.enum(["breakfast", "lunch", "dinner", "snack"]),
  date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Date must be in YYYY-MM-DD format"),
});

export const updateMealSchema = createInsertSchema(meals).pick({
  name: true,
  notes: true,
}).partial();

export type InsertMeal = z.infer<typeof insertMealSchema>;
export type UpdateMeal = z.infer<typeof updateMealSchema>;
export type Meal = typeof meals.$inferSelect;

export const mealItems = pgTable("meal_items", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  mealId: varchar("meal_id").notNull(), // Reference to meal
  foodId: varchar("food_id").notNull(), // Reference to food
  grams: decimal("grams", { precision: 8, scale: 2 }).notNull(), // Amount in grams
  
  // Snapshot nutritional values (for historical accuracy)
  calories: decimal("calories", { precision: 8, scale: 2 }).notNull(),
  protein: decimal("protein", { precision: 8, scale: 2 }).notNull(),
  carbs: decimal("carbs", { precision: 8, scale: 2 }).notNull(),
  fat: decimal("fat", { precision: 8, scale: 2 }).notNull(),
  
  createdAt: timestamp("created_at").notNull().defaultNow(),
});

export const insertMealItemSchema = createInsertSchema(mealItems).omit({
  id: true,
  calories: true,
  protein: true,
  carbs: true,
  fat: true,
  createdAt: true,
}).extend({
  grams: z.coerce.number().min(0.1).max(10000),
});

export const updateMealItemSchema = createInsertSchema(mealItems).pick({
  grams: true,
}).extend({
  grams: z.coerce.number().min(0.1).max(10000),
});

export type InsertMealItem = z.infer<typeof insertMealItemSchema>;
export type UpdateMealItem = z.infer<typeof updateMealItemSchema>;
export type MealItem = typeof mealItems.$inferSelect;

export const dailyNutritionSummary = pgTable("daily_nutrition_summary", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  userId: varchar("user_id").notNull(), // Reference to user
  date: text("date").notNull(), // YYYY-MM-DD format
  
  // Daily totals
  totalCalories: decimal("total_calories", { precision: 8, scale: 2 }).notNull().default("0"),
  totalProtein: decimal("total_protein", { precision: 8, scale: 2 }).notNull().default("0"),
  totalCarbs: decimal("total_carbs", { precision: 8, scale: 2 }).notNull().default("0"),
  totalFat: decimal("total_fat", { precision: 8, scale: 2 }).notNull().default("0"),
  totalFiber: decimal("total_fiber", { precision: 8, scale: 2 }).notNull().default("0"),
  totalSodium: decimal("total_sodium", { precision: 8, scale: 2 }).notNull().default("0"),
  
  // Goal tracking
  caloriesGoal: decimal("calories_goal", { precision: 8, scale: 2 }),
  proteinGoal: decimal("protein_goal", { precision: 8, scale: 2 }),
  carbsGoal: decimal("carbs_goal", { precision: 8, scale: 2 }),
  fatGoal: decimal("fat_goal", { precision: 8, scale: 2 }),
  
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

export type DailyNutritionSummary = typeof dailyNutritionSummary.$inferSelect;

export const mealPlans = pgTable("meal_plans", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  userId: varchar("user_id").notNull(), // Reference to user
  name: text("name").notNull(),
  description: text("description"),
  
  // Plan metadata
  targetCalories: decimal("target_calories", { precision: 8, scale: 2 }),
  targetProtein: decimal("target_protein", { precision: 8, scale: 2 }),
  targetCarbs: decimal("target_carbs", { precision: 8, scale: 2 }),
  targetFat: decimal("target_fat", { precision: 8, scale: 2 }),
  
  // AI generation metadata
  isAIGenerated: boolean("is_ai_generated").notNull().default(false),
  generationPrompt: text("generation_prompt"), // What the user asked for
  dietType: text("diet_type"), // general, vegetarian, vegan, keto, etc.
  restrictions: text("restrictions").array().default([]), // allergies, dislikes
  
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

export const insertMealPlanSchema = createInsertSchema(mealPlans).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
}).extend({
  targetCalories: z.coerce.number().min(800).max(5000).optional(),
  targetProtein: z.coerce.number().min(20).max(300).optional(),
  targetCarbs: z.coerce.number().min(50).max(600).optional(),
  targetFat: z.coerce.number().min(20).max(200).optional(),
  dietType: z.enum(["general", "vegetarian", "vegan", "keto", "paleo", "mediterranean", "low_carb"]).optional(),
  restrictions: z.array(z.string()).default([]),
});

export type InsertMealPlan = z.infer<typeof insertMealPlanSchema>;
export type MealPlan = typeof mealPlans.$inferSelect;

export const mealPlanItems = pgTable("meal_plan_items", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  mealPlanId: varchar("meal_plan_id").notNull(), // Reference to meal plan
  mealType: text("meal_type").notNull(), // breakfast, lunch, dinner, snack
  foodId: varchar("food_id").notNull(), // Reference to food
  grams: decimal("grams", { precision: 8, scale: 2 }).notNull(), // Planned amount in grams
  orderInMeal: integer("order_in_meal").notNull().default(0), // For ordering items within a meal
  
  // Snapshot nutritional values
  calories: decimal("calories", { precision: 8, scale: 2 }).notNull(),
  protein: decimal("protein", { precision: 8, scale: 2 }).notNull(),
  carbs: decimal("carbs", { precision: 8, scale: 2 }).notNull(),
  fat: decimal("fat", { precision: 8, scale: 2 }).notNull(),
});

export const insertMealPlanItemSchema = createInsertSchema(mealPlanItems).omit({
  id: true,
  calories: true,
  protein: true,
  carbs: true,
  fat: true,
}).extend({
  mealType: z.enum(["breakfast", "lunch", "dinner", "snack"]),
  grams: z.coerce.number().min(0.1).max(10000),
});

export type InsertMealPlanItem = z.infer<typeof insertMealPlanItemSchema>;
export type MealPlanItem = typeof mealPlanItems.$inferSelect;
