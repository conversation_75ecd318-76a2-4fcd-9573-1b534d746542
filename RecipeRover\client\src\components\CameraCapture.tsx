import { useRef, useState, useCallback, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Camera, Video, Square, RotateCcw, AlertCircle } from 'lucide-react';

interface CameraCaptureProps {
  onImageCapture?: (imageData: string) => void;
  onVideoFrame?: (imageData: string) => void;
  isAnalyzing?: boolean;
  exercise?: string;
}

export default function CameraCapture({ 
  onImageCapture, 
  onVideoFrame, 
  isAnalyzing = false, 
  exercise = "Exercise" 
}: CameraCaptureProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const isRecordingRef = useRef(false);
  const isAnalyzingRef = useRef(false);
  const lastFrameTimeRef = useRef(0);
  const [isStreaming, setIsStreaming] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [error, setError] = useState<string>('');

  // Update ref when isAnalyzing prop changes
  useEffect(() => {
    isAnalyzingRef.current = isAnalyzing;
  }, [isAnalyzing]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopCamera();
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, []);

  const startCamera = useCallback(async () => {
    try {
      setError('');
      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: { 
          width: { ideal: 1280 },
          height: { ideal: 720 },
          facingMode: 'user'
        },
        audio: false 
      });
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        setIsStreaming(true);
      }
    } catch (err) {
      setError('Unable to access camera. Please check permissions.');
      console.error('Camera access error:', err);
    }
  }, []);

  const stopCamera = useCallback(() => {
    if (videoRef.current?.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream;
      stream.getTracks().forEach(track => track.stop());
      videoRef.current.srcObject = null;
      setIsStreaming(false);
      setIsRecording(false);
    }
  }, []);

  const captureImage = useCallback(() => {
    if (!videoRef.current || !canvasRef.current) return;

    const canvas = canvasRef.current;
    const video = videoRef.current;
    const ctx = canvas.getContext('2d');
    
    if (!ctx) return;

    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    ctx.drawImage(video, 0, 0);
    
    const imageData = canvas.toDataURL('image/jpeg', 0.8);
    onImageCapture?.(imageData);
    console.log('Image captured for analysis');
  }, [onImageCapture]);

  const startRecording = useCallback(() => {
    if (!isStreaming || isAnalyzingRef.current) return;
    
    setIsRecording(true);
    isRecordingRef.current = true;
    lastFrameTimeRef.current = 0;
    
    // Capture frames every 5 seconds with throttling for cost control
    const frameInterval = setInterval(() => {
      if (!isRecordingRef.current || !videoRef.current || !canvasRef.current) {
        clearInterval(frameInterval);
        return;
      }

      // Skip if currently analyzing to prevent overwhelming the API
      if (isAnalyzingRef.current) {
        return;
      }

      // Throttle to prevent rapid consecutive calls
      const now = Date.now();
      if (now - lastFrameTimeRef.current < 5000) {
        return;
      }
      lastFrameTimeRef.current = now;

      const canvas = canvasRef.current;
      const video = videoRef.current;
      const ctx = canvas.getContext('2d');
      
      if (!ctx) return;

      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      ctx.drawImage(video, 0, 0);
      
      const imageData = canvas.toDataURL('image/jpeg', 0.6);
      onVideoFrame?.(imageData);
    }, 1000); // Check every second but throttle to 5 seconds

    intervalRef.current = frameInterval;
  }, [isStreaming, isAnalyzing, onVideoFrame]);

  const stopRecording = useCallback(() => {
    setIsRecording(false);
    isRecordingRef.current = false;
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  }, []);

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Camera className="h-5 w-5 text-primary" />
          Form Analysis Camera
        </CardTitle>
        <CardDescription>
          Position yourself in frame for real-time form analysis during {exercise}
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Video Stream */}
        <div className="relative bg-muted rounded-lg overflow-hidden aspect-video">
          <video
            ref={videoRef}
            autoPlay
            playsInline
            muted
            className="w-full h-full object-cover"
            style={{ transform: 'scaleX(-1)' }} // Mirror view for user
          />
          
          {/* Status Overlays */}
          {isRecording && (
            <div className="absolute top-4 left-4">
              <Badge variant="destructive" className="animate-pulse">
                <div className="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
                Recording
              </Badge>
            </div>
          )}
          
          {isAnalyzing && (
            <div className="absolute top-4 right-4">
              <Badge variant="secondary">
                <div className="w-2 h-2 bg-blue-500 rounded-full mr-2 animate-pulse"></div>
                Analyzing...
              </Badge>
            </div>
          )}

          {!isStreaming && (
            <div className="absolute inset-0 flex items-center justify-center bg-muted/80">
              <div className="text-center">
                <Camera className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">Camera not active</p>
              </div>
            </div>
          )}
        </div>

        {/* Error Message */}
        {error && (
          <div className="flex items-center gap-2 p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
            <AlertCircle className="h-4 w-4 text-destructive" />
            <span className="text-sm text-destructive">{error}</span>
          </div>
        )}

        {/* Camera Controls */}
        <div className="flex flex-wrap gap-2 justify-center">
          {!isStreaming ? (
            <Button 
              onClick={startCamera}
              data-testid="button-start-camera"
            >
              <Camera className="mr-2 h-4 w-4" />
              Start Camera
            </Button>
          ) : (
            <>
              <Button 
                onClick={stopCamera}
                variant="outline"
                data-testid="button-stop-camera"
              >
                <Square className="mr-2 h-4 w-4" />
                Stop Camera
              </Button>
              
              <Button 
                onClick={captureImage}
                data-testid="button-capture-image"
              >
                <Camera className="mr-2 h-4 w-4" />
                Analyze Form
              </Button>
              
              {!isRecording ? (
                <Button 
                  onClick={startRecording}
                  variant="destructive"
                  data-testid="button-start-recording"
                >
                  <Video className="mr-2 h-4 w-4" />
                  Live Analysis
                </Button>
              ) : (
                <Button 
                  onClick={stopRecording}
                  variant="outline"
                  data-testid="button-stop-recording"
                >
                  <Square className="mr-2 h-4 w-4" />
                  Stop Analysis
                </Button>
              )}
              
              <Button 
                onClick={() => window.location.reload()}
                variant="ghost"
                size="icon"
                data-testid="button-reset-camera"
              >
                <RotateCcw className="h-4 w-4" />
              </Button>
            </>
          )}
        </div>

        {/* Hidden canvas for image capture */}
        <canvas ref={canvasRef} className="hidden" />
      </CardContent>
    </Card>
  );
}