import { useState } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { format, startOfWeek, endOfWeek, subDays, addDays } from 'date-fns';

// UI Components
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';

// Icons
import { 
  Calendar, 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  Target, 
  TrendingUp, 
  Coffee, 
  Sun, 
  Moon, 
  Apple,
  ChefHat,
  BarChart3,
  Sparkles,
  Clock,
  CheckCircle
} from 'lucide-react';

// Recharts for analytics
import { PieChart, Pie, Cell, LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts';

// API and utilities
import { apiRequest, queryClient } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { getCurrentUserId } from '@/lib/currentUser';

// Type definitions
interface Food {
  id: string;
  name: string;
  category: string;
  caloriesPerHundredGrams: number;
  proteinPerHundredGrams: number;
  carbsPerHundredGrams: number;
  fatPerHundredGrams: number;
  fiberPerHundredGrams: number;
  sodiumPerHundredGrams: number;
  isUserCreated: boolean;
  portions?: FoodPortion[];
}

interface FoodPortion {
  id: string;
  foodId: string;
  name: string;
  gramsEquivalent: number;
  isDefault: boolean;
}

interface MealItem {
  id: string;
  mealId: string;
  foodId: string;
  grams: number;
  food?: Food;
}

interface Meal {
  id: string;
  userId: string;
  date: string;
  mealType: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  name: string;
  items: MealItem[];
}

interface NutritionGoals {
  calorieTarget?: number;
  proteinTarget?: number;
  carbTarget?: number;
  fatTarget?: number;
  dietType?: string;
  restrictions: string[];
  preferredCuisines: string[];
}

interface MealPlan {
  id: string;
  userId: string;
  name: string;
  description: string;
  targetCalories: number;
  targetProtein: number;
  targetCarbs: number;
  targetFat: number;
  isAIGenerated: boolean;
  items?: any[];
}

// Form schemas
const createFoodSchema = z.object({
  name: z.string().min(1, 'Food name is required'),
  category: z.string().min(1, 'Category is required'),
  caloriesPerHundredGrams: z.number().min(0),
  proteinPerHundredGrams: z.number().min(0),
  carbsPerHundredGrams: z.number().min(0),
  fatPerHundredGrams: z.number().min(0),
  fiberPerHundredGrams: z.number().min(0).default(0),
  sodiumPerHundredGrams: z.number().min(0).default(0),
  description: z.string().optional(),
});

const addMealItemSchema = z.object({
  foodId: z.string(),
  grams: z.number().min(1, 'Amount must be greater than 0'),
});

const createMealSchema = z.object({
  mealType: z.enum(['breakfast', 'lunch', 'dinner', 'snack']),
  name: z.string().min(1, 'Meal name is required'),
  date: z.string(),
});

const generateMealPlanSchema = z.object({
  prompt: z.string().min(1, 'Please describe what kind of meal plan you want'),
  targetCalories: z.number().min(800).max(5000).optional(),
  dietType: z.enum(['general', 'vegetarian', 'vegan', 'keto', 'paleo', 'mediterranean', 'low_carb']).optional(),
  restrictions: z.array(z.string()).default([]),
  preferredCuisines: z.array(z.string()).default([]),
  mealsToInclude: z.array(z.enum(['breakfast', 'lunch', 'dinner', 'snack'])).default(['breakfast', 'lunch', 'dinner']),
});

export default function NutritionPage() {
  const { toast } = useToast();
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [searchQuery, setSearchQuery] = useState('');
  const [showCreateFoodDialog, setShowCreateFoodDialog] = useState(false);
  const [showAddMealDialog, setShowAddMealDialog] = useState(false);
  const [showAddItemDialog, setShowAddItemDialog] = useState(false);
  const [showGeneratePlanDialog, setShowGeneratePlanDialog] = useState(false);
  const [selectedMeal, setSelectedMeal] = useState<string | null>(null);
  const [selectedFood, setSelectedFood] = useState<Food | null>(null);

  // Get current user ID using consistent pattern
  const userId = getCurrentUserId();

  // Queries
  const { data: meals = [], isLoading: mealsLoading } = useQuery({
    queryKey: ['/api/nutrition/meals', selectedDate, userId],
    queryFn: () => apiRequest(`/api/nutrition/meals?date=${selectedDate}&userId=${userId}`),
  });

  const { data: foods = [], isLoading: foodsLoading } = useQuery({
    queryKey: ['/api/nutrition/foods', searchQuery],
    queryFn: () => apiRequest(`/api/nutrition/foods${searchQuery ? `?q=${searchQuery}` : ''}`),
  });

  const { data: nutritionGoals } = useQuery({
    queryKey: ['/api/nutrition/goals', userId],
    queryFn: () => apiRequest(`/api/nutrition/goals/${userId}`),
  });

  const { data: mealPlans = [] } = useQuery({
    queryKey: ['/api/nutrition/plans', userId],
    queryFn: () => apiRequest(`/api/nutrition/plans/${userId}`),
  });

  const { data: nutritionSummary } = useQuery({
    queryKey: ['/api/nutrition/summary', userId, selectedDate],
    queryFn: () => apiRequest(`/api/nutrition/summary?userId=${userId}`),
  });

  const { data: weeklyData = [] } = useQuery({
    queryKey: ['/api/nutrition/summary', userId, 'weekly'],
    queryFn: () => {
      const weekStart = format(startOfWeek(new Date(selectedDate)), 'yyyy-MM-dd');
      const weekEnd = format(endOfWeek(new Date(selectedDate)), 'yyyy-MM-dd');
      return apiRequest(`/api/nutrition/summary?userId=${userId}&startDate=${weekStart}&endDate=${weekEnd}`);
    },
  });

  // Mutations
  const createFoodMutation = useMutation({
    mutationFn: (data: any) => apiRequest('/api/nutrition/foods', { method: 'POST', body: data }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/nutrition/foods'] });
      setShowCreateFoodDialog(false);
      toast({ title: 'Food created successfully!' });
    },
    onError: () => toast({ title: 'Failed to create food', variant: 'destructive' }),
  });

  const createMealMutation = useMutation({
    mutationFn: (data: any) => apiRequest('/api/nutrition/meals', { method: 'POST', body: { ...data, userId } }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/nutrition/meals'] });
      queryClient.invalidateQueries({ queryKey: ['/api/nutrition/summary', userId] });
      setShowAddMealDialog(false);
      toast({ title: 'Meal created successfully!' });
    },
    onError: () => toast({ title: 'Failed to create meal', variant: 'destructive' }),
  });

  const addMealItemMutation = useMutation({
    mutationFn: ({ mealId, data }: { mealId: string; data: any }) => 
      apiRequest(`/api/nutrition/meals/${mealId}/items`, { method: 'POST', body: data }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/nutrition/meals'] });
      queryClient.invalidateQueries({ queryKey: ['/api/nutrition/summary', userId] });
      setShowAddItemDialog(false);
      setSelectedMeal(null);
      setSelectedFood(null);
      toast({ title: 'Food added to meal!' });
    },
    onError: () => toast({ title: 'Failed to add food to meal', variant: 'destructive' }),
  });

  const deleteMealMutation = useMutation({
    mutationFn: (mealId: string) => apiRequest(`/api/nutrition/meals/${mealId}`, { method: 'DELETE' }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/nutrition/meals'] });
      queryClient.invalidateQueries({ queryKey: ['/api/nutrition/summary', userId] });
      toast({ title: 'Meal deleted successfully!' });
    },
    onError: () => toast({ title: 'Failed to delete meal', variant: 'destructive' }),
  });

  const generateMealPlanMutation = useMutation({
    mutationFn: (data: any) => apiRequest('/api/nutrition/plan/generate', { method: 'POST', body: { ...data, userId } }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/nutrition/plans'] });
      queryClient.invalidateQueries({ queryKey: ['/api/nutrition/foods'] });
      setShowGeneratePlanDialog(false);
      toast({ title: 'Meal plan generated successfully!' });
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to generate meal plan';
      toast({ title: message, variant: 'destructive' });
    },
  });

  const applyMealPlanMutation = useMutation({
    mutationFn: ({ planId, date }: { planId: string; date: string }) => 
      apiRequest(`/api/nutrition/plans/${planId}/apply`, { method: 'POST', body: { userId, date } }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/nutrition/meals'] });
      queryClient.invalidateQueries({ queryKey: ['/api/nutrition/summary', userId] });
      queryClient.invalidateQueries({ queryKey: ['/api/nutrition/plans'] });
      toast({ title: 'Meal plan applied to your log!' });
    },
    onError: () => toast({ title: 'Failed to apply meal plan', variant: 'destructive' }),
  });

  // Forms
  const createFoodForm = useForm({
    resolver: zodResolver(createFoodSchema),
    defaultValues: {
      name: '',
      category: 'other',
      caloriesPerHundredGrams: 0,
      proteinPerHundredGrams: 0,
      carbsPerHundredGrams: 0,
      fatPerHundredGrams: 0,
      fiberPerHundredGrams: 0,
      sodiumPerHundredGrams: 0,
      description: '',
    },
  });

  const createMealForm = useForm({
    resolver: zodResolver(createMealSchema),
    defaultValues: {
      mealType: 'breakfast' as const,
      name: '',
      date: selectedDate,
    },
  });

  const addMealItemForm = useForm({
    resolver: zodResolver(addMealItemSchema),
    defaultValues: {
      foodId: '',
      grams: 100,
    },
  });

  const generateMealPlanForm = useForm({
    resolver: zodResolver(generateMealPlanSchema),
    defaultValues: {
      prompt: '',
      targetCalories: nutritionGoals?.calorieTarget || 2000,
      dietType: nutritionGoals?.dietType as any || 'general',
      restrictions: nutritionGoals?.restrictions || [],
      preferredCuisines: nutritionGoals?.preferredCuisines || [],
      mealsToInclude: ['breakfast', 'lunch', 'dinner'] as const,
    },
  });

  // Helper functions
  const calculateMealNutrition = (meal: Meal) => {
    return meal.items.reduce((total, item) => {
      if (!item.food) return total;
      const multiplier = item.grams / 100;
      return {
        calories: total.calories + (item.food.caloriesPerHundredGrams * multiplier),
        protein: total.protein + (item.food.proteinPerHundredGrams * multiplier),
        carbs: total.carbs + (item.food.carbsPerHundredGrams * multiplier),
        fat: total.fat + (item.food.fatPerHundredGrams * multiplier),
      };
    }, { calories: 0, protein: 0, carbs: 0, fat: 0 });
  };

  const calculateDailyNutrition = () => {
    return meals.reduce((total, meal) => {
      const mealNutrition = calculateMealNutrition(meal);
      return {
        calories: total.calories + mealNutrition.calories,
        protein: total.protein + mealNutrition.protein,
        carbs: total.carbs + mealNutrition.carbs,
        fat: total.fat + mealNutrition.fat,
      };
    }, { calories: 0, protein: 0, carbs: 0, fat: 0 });
  };

  const getMealIcon = (mealType: string) => {
    switch (mealType) {
      case 'breakfast': return <Coffee className="w-4 h-4" />;
      case 'lunch': return <Sun className="w-4 h-4" />;
      case 'dinner': return <Moon className="w-4 h-4" />;
      case 'snack': return <Apple className="w-4 h-4" />;
      default: return <Coffee className="w-4 h-4" />;
    }
  };

  const dailyNutrition = calculateDailyNutrition();
  const calorieTarget = nutritionGoals?.calorieTarget || 2000;
  const proteinTarget = nutritionGoals?.proteinTarget || 150;
  const carbTarget = nutritionGoals?.carbTarget || 250;
  const fatTarget = nutritionGoals?.fatTarget || 65;

  // Macro breakdown data for pie chart
  const macroData = [
    { name: 'Protein', value: dailyNutrition.protein * 4, fill: '#8884d8' },
    { name: 'Carbs', value: dailyNutrition.carbs * 4, fill: '#82ca9d' },
    { name: 'Fat', value: dailyNutrition.fat * 9, fill: '#ffc658' },
  ];

  return (
    <>
      {/* SEO Meta tags */}
      <title>Nutrition Tracking - Circuit Fitness</title>
      <meta name="description" content="Track your daily nutrition, log meals, create custom foods, and generate AI-powered meal plans. Monitor macros, calories, and achieve your dietary goals." />
      
      <div className="container mx-auto p-4 space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold" data-testid="text-page-title">Nutrition Tracking</h1>
            <p className="text-muted-foreground">Track your meals, plan your nutrition, and achieve your goals</p>
          </div>
          <div className="flex items-center gap-2">
            <Calendar className="w-4 h-4" />
            <Input
              type="date"
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
              data-testid="input-date-picker"
              className="w-auto"
            />
          </div>
        </div>

        <Tabs defaultValue="log" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="log" data-testid="tab-log">
            <Coffee className="w-4 h-4 mr-2" />
            Daily Log
          </TabsTrigger>
          <TabsTrigger value="foods" data-testid="tab-foods">
            <Apple className="w-4 h-4 mr-2" />
            Foods
          </TabsTrigger>
          <TabsTrigger value="plans" data-testid="tab-plans">
            <ChefHat className="w-4 h-4 mr-2" />
            Meal Plans
          </TabsTrigger>
          <TabsTrigger value="analytics" data-testid="tab-analytics">
            <BarChart3 className="w-4 h-4 mr-2" />
            Analytics
          </TabsTrigger>
        </TabsList>

        {/* LOG TAB */}
        <TabsContent value="log" className="space-y-6">
          {/* Daily Progress Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="w-5 h-5" />
                Daily Progress
              </CardTitle>
              <CardDescription>Your nutrition goals for {format(new Date(selectedDate), 'MMMM d, yyyy')}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Calories</span>
                    <span>{Math.round(dailyNutrition.calories)} / {calorieTarget}</span>
                  </div>
                  <Progress value={(dailyNutrition.calories / calorieTarget) * 100} className="h-2" />
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Protein</span>
                    <span>{Math.round(dailyNutrition.protein)}g / {proteinTarget}g</span>
                  </div>
                  <Progress value={(dailyNutrition.protein / proteinTarget) * 100} className="h-2" />
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Carbs</span>
                    <span>{Math.round(dailyNutrition.carbs)}g / {carbTarget}g</span>
                  </div>
                  <Progress value={(dailyNutrition.carbs / carbTarget) * 100} className="h-2" />
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Fat</span>
                    <span>{Math.round(dailyNutrition.fat)}g / {fatTarget}g</span>
                  </div>
                  <Progress value={(dailyNutrition.fat / fatTarget) * 100} className="h-2" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Meals */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold">Meals</h2>
              <Dialog open={showAddMealDialog} onOpenChange={setShowAddMealDialog}>
                <DialogTrigger asChild>
                  <Button data-testid="button-add-meal">
                    <Plus className="w-4 h-4 mr-2" />
                    Add Meal
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Add New Meal</DialogTitle>
                    <DialogDescription>Create a new meal for {format(new Date(selectedDate), 'MMMM d, yyyy')}</DialogDescription>
                  </DialogHeader>
                  <Form {...createMealForm}>
                    <form onSubmit={createMealForm.handleSubmit((data) => createMealMutation.mutate(data))} className="space-y-4">
                      <FormField
                        control={createMealForm.control}
                        name="mealType"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Meal Type</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger data-testid="select-meal-type">
                                  <SelectValue placeholder="Select meal type" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="breakfast">Breakfast</SelectItem>
                                <SelectItem value="lunch">Lunch</SelectItem>
                                <SelectItem value="dinner">Dinner</SelectItem>
                                <SelectItem value="snack">Snack</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={createMealForm.control}
                        name="name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Meal Name</FormLabel>
                            <FormControl>
                              <Input placeholder="e.g., Protein Smoothie" {...field} data-testid="input-meal-name" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <Button type="submit" disabled={createMealMutation.isPending} data-testid="button-create-meal">
                        {createMealMutation.isPending ? 'Creating...' : 'Create Meal'}
                      </Button>
                    </form>
                  </Form>
                </DialogContent>
              </Dialog>
            </div>

            {mealsLoading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {[1, 2, 3, 4].map((i) => (
                  <Card key={i} className="animate-pulse">
                    <div className="h-32 bg-muted rounded-lg"></div>
                  </Card>
                ))}
              </div>
            ) : meals.length === 0 ? (
              <Card>
                <CardContent className="text-center py-8">
                  <p className="text-muted-foreground">No meals logged for this day</p>
                  <p className="text-sm text-muted-foreground mt-2">Add your first meal to start tracking</p>
                </CardContent>
              </Card>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {meals.map((meal) => {
                  const mealNutrition = calculateMealNutrition(meal);
                  return (
                    <Card key={meal.id} data-testid={`card-meal-${meal.id}`}>
                      <CardHeader className="pb-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            {getMealIcon(meal.mealType)}
                            <div>
                              <CardTitle className="text-lg">{meal.name}</CardTitle>
                              <CardDescription className="capitalize">{meal.mealType}</CardDescription>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => {
                                setSelectedMeal(meal.id);
                                setShowAddItemDialog(true);
                              }}
                              data-testid={`button-add-food-${meal.id}`}
                            >
                              <Plus className="w-3 h-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => deleteMealMutation.mutate(meal.id)}
                              data-testid={`button-delete-meal-${meal.id}`}
                            >
                              <Trash2 className="w-3 h-3" />
                            </Button>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        {meal.items.length === 0 ? (
                          <p className="text-sm text-muted-foreground">No foods added yet</p>
                        ) : (
                          <>
                            <div className="space-y-2">
                              {meal.items.map((item) => (
                                <div key={item.id} className="flex items-center justify-between text-sm">
                                  <span>{item.food?.name}</span>
                                  <span className="text-muted-foreground">{item.grams}g</span>
                                </div>
                              ))}
                            </div>
                            <Separator />
                            <div className="grid grid-cols-4 gap-2 text-sm">
                              <div className="text-center">
                                <div className="font-medium">{Math.round(mealNutrition.calories)}</div>
                                <div className="text-muted-foreground">cal</div>
                              </div>
                              <div className="text-center">
                                <div className="font-medium">{Math.round(mealNutrition.protein)}g</div>
                                <div className="text-muted-foreground">protein</div>
                              </div>
                              <div className="text-center">
                                <div className="font-medium">{Math.round(mealNutrition.carbs)}g</div>
                                <div className="text-muted-foreground">carbs</div>
                              </div>
                              <div className="text-center">
                                <div className="font-medium">{Math.round(mealNutrition.fat)}g</div>
                                <div className="text-muted-foreground">fat</div>
                              </div>
                            </div>
                          </>
                        )}
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            )}
          </div>

          {/* Add Food to Meal Dialog */}
          <Dialog open={showAddItemDialog} onOpenChange={setShowAddItemDialog}>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Add Food to Meal</DialogTitle>
                <DialogDescription>Search for a food and specify the amount</DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search foods..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                    data-testid="input-food-search"
                  />
                </div>
                
                {selectedFood ? (
                  <Form {...addMealItemForm}>
                    <form onSubmit={addMealItemForm.handleSubmit((data) => 
                      selectedMeal && addMealItemMutation.mutate({ mealId: selectedMeal, data: { ...data, foodId: selectedFood.id } })
                    )} className="space-y-4">
                      <Card>
                        <CardContent className="pt-4">
                          <div className="flex items-center justify-between mb-4">
                            <div>
                              <h3 className="font-medium">{selectedFood.name}</h3>
                              <p className="text-sm text-muted-foreground capitalize">{selectedFood.category}</p>
                            </div>
                            <Button variant="outline" size="sm" onClick={() => setSelectedFood(null)}>
                              Change
                            </Button>
                          </div>
                          <div className="grid grid-cols-4 gap-2 text-sm text-center">
                            <div>
                              <div className="font-medium">{selectedFood.caloriesPerHundredGrams}</div>
                              <div className="text-muted-foreground">cal/100g</div>
                            </div>
                            <div>
                              <div className="font-medium">{selectedFood.proteinPerHundredGrams}g</div>
                              <div className="text-muted-foreground">protein</div>
                            </div>
                            <div>
                              <div className="font-medium">{selectedFood.carbsPerHundredGrams}g</div>
                              <div className="text-muted-foreground">carbs</div>
                            </div>
                            <div>
                              <div className="font-medium">{selectedFood.fatPerHundredGrams}g</div>
                              <div className="text-muted-foreground">fat</div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                      
                      <FormField
                        control={addMealItemForm.control}
                        name="grams"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Amount (grams)</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                placeholder="100"
                                {...field}
                                onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                                data-testid="input-food-grams"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <Button type="submit" disabled={addMealItemMutation.isPending} data-testid="button-add-food-item">
                        {addMealItemMutation.isPending ? 'Adding...' : 'Add to Meal'}
                      </Button>
                    </form>
                  </Form>
                ) : (
                  <div className="max-h-60 overflow-y-auto space-y-2">
                    {foodsLoading ? (
                      <div className="space-y-2">
                        {[1, 2, 3].map((i) => (
                          <div key={i} className="h-16 bg-muted rounded animate-pulse"></div>
                        ))}
                      </div>
                    ) : foods.length === 0 ? (
                      <p className="text-center text-muted-foreground py-4">
                        {searchQuery ? 'No foods found' : 'Start typing to search foods'}
                      </p>
                    ) : (
                      foods.map((food) => (
                        <Card key={food.id} className="cursor-pointer hover-elevate" onClick={() => setSelectedFood(food)} data-testid={`card-food-${food.id}`}>
                          <CardContent className="p-4">
                            <div className="flex items-center justify-between">
                              <div>
                                <h3 className="font-medium">{food.name}</h3>
                                <p className="text-sm text-muted-foreground capitalize">{food.category}</p>
                              </div>
                              <div className="text-right">
                                <div className="font-medium">{food.caloriesPerHundredGrams} cal</div>
                                <div className="text-sm text-muted-foreground">per 100g</div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))
                    )}
                  </div>
                )}
              </div>
            </DialogContent>
          </Dialog>
        </TabsContent>

        {/* FOODS TAB */}
        <TabsContent value="foods" className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Food Database</h2>
            <Dialog open={showCreateFoodDialog} onOpenChange={setShowCreateFoodDialog}>
              <DialogTrigger asChild>
                <Button data-testid="button-create-food">
                  <Plus className="w-4 h-4 mr-2" />
                  Create Food
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create Custom Food</DialogTitle>
                  <DialogDescription>Add a new food to your database</DialogDescription>
                </DialogHeader>
                <Form {...createFoodForm}>
                  <form onSubmit={createFoodForm.handleSubmit((data) => createFoodMutation.mutate(data))} className="space-y-4">
                    <FormField
                      control={createFoodForm.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Food Name</FormLabel>
                          <FormControl>
                            <Input placeholder="e.g., Grilled Chicken Breast" {...field} data-testid="input-food-name" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={createFoodForm.control}
                      name="category"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Category</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger data-testid="select-food-category">
                                <SelectValue placeholder="Select category" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="protein">Protein</SelectItem>
                              <SelectItem value="dairy">Dairy</SelectItem>
                              <SelectItem value="grains">Grains</SelectItem>
                              <SelectItem value="vegetables">Vegetables</SelectItem>
                              <SelectItem value="fruits">Fruits</SelectItem>
                              <SelectItem value="nuts">Nuts & Seeds</SelectItem>
                              <SelectItem value="beverages">Beverages</SelectItem>
                              <SelectItem value="snacks">Snacks</SelectItem>
                              <SelectItem value="other">Other</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={createFoodForm.control}
                        name="caloriesPerHundredGrams"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Calories (per 100g)</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                placeholder="200"
                                {...field}
                                onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                                data-testid="input-calories"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={createFoodForm.control}
                        name="proteinPerHundredGrams"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Protein (g per 100g)</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                placeholder="25"
                                {...field}
                                onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                                data-testid="input-protein"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={createFoodForm.control}
                        name="carbsPerHundredGrams"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Carbs (g per 100g)</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                placeholder="0"
                                {...field}
                                onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                                data-testid="input-carbs"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={createFoodForm.control}
                        name="fatPerHundredGrams"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Fat (g per 100g)</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                placeholder="5"
                                {...field}
                                onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                                data-testid="input-fat"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    <Button type="submit" disabled={createFoodMutation.isPending} data-testid="button-submit-food">
                      {createFoodMutation.isPending ? 'Creating...' : 'Create Food'}
                    </Button>
                  </form>
                </Form>
              </DialogContent>
            </Dialog>
          </div>

          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search foods..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
              data-testid="input-foods-search"
            />
          </div>

          {foodsLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {[1, 2, 3, 4, 5, 6].map((i) => (
                <Card key={i} className="animate-pulse">
                  <div className="h-32 bg-muted rounded-lg"></div>
                </Card>
              ))}
            </div>
          ) : foods.length === 0 ? (
            <Card>
              <CardContent className="text-center py-8">
                <p className="text-muted-foreground">
                  {searchQuery ? 'No foods found matching your search' : 'No foods in database'}
                </p>
                <p className="text-sm text-muted-foreground mt-2">Create your first custom food to get started</p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {foods.map((food) => (
                <Card key={food.id} data-testid={`card-food-item-${food.id}`}>
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="text-lg">{food.name}</CardTitle>
                        <CardDescription className="capitalize">{food.category}</CardDescription>
                      </div>
                      {food.isUserCreated && (
                        <Badge variant="secondary" className="text-xs">Custom</Badge>
                      )}
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div className="text-center">
                        <div className="font-medium">{food.caloriesPerHundredGrams}</div>
                        <div className="text-muted-foreground">calories</div>
                      </div>
                      <div className="text-center">
                        <div className="font-medium">{food.proteinPerHundredGrams}g</div>
                        <div className="text-muted-foreground">protein</div>
                      </div>
                      <div className="text-center">
                        <div className="font-medium">{food.carbsPerHundredGrams}g</div>
                        <div className="text-muted-foreground">carbs</div>
                      </div>
                      <div className="text-center">
                        <div className="font-medium">{food.fatPerHundredGrams}g</div>
                        <div className="text-muted-foreground">fat</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        {/* PLANS TAB */}
        <TabsContent value="plans" className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Meal Plans</h2>
            <Dialog open={showGeneratePlanDialog} onOpenChange={setShowGeneratePlanDialog}>
              <DialogTrigger asChild>
                <Button data-testid="button-generate-plan">
                  <Sparkles className="w-4 h-4 mr-2" />
                  Generate AI Plan
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>Generate AI Meal Plan</DialogTitle>
                  <DialogDescription>Let AI create a personalized meal plan for you</DialogDescription>
                </DialogHeader>
                <Form {...generateMealPlanForm}>
                  <form onSubmit={generateMealPlanForm.handleSubmit((data) => generateMealPlanMutation.mutate(data))} className="space-y-4">
                    <FormField
                      control={generateMealPlanForm.control}
                      name="prompt"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Describe your ideal meal plan</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="e.g., I want a high-protein meal plan for muscle building with 2000 calories"
                              {...field}
                              data-testid="textarea-meal-plan-prompt"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={generateMealPlanForm.control}
                        name="targetCalories"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Target Calories</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                placeholder="2000"
                                {...field}
                                onChange={(e) => field.onChange(parseFloat(e.target.value) || 2000)}
                                data-testid="input-target-calories"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={generateMealPlanForm.control}
                        name="dietType"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Diet Type</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger data-testid="select-diet-type">
                                  <SelectValue placeholder="Select diet" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="general">General</SelectItem>
                                <SelectItem value="vegetarian">Vegetarian</SelectItem>
                                <SelectItem value="vegan">Vegan</SelectItem>
                                <SelectItem value="keto">Keto</SelectItem>
                                <SelectItem value="paleo">Paleo</SelectItem>
                                <SelectItem value="mediterranean">Mediterranean</SelectItem>
                                <SelectItem value="low_carb">Low Carb</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    <Button type="submit" disabled={generateMealPlanMutation.isPending} className="w-full" data-testid="button-submit-generate">
                      {generateMealPlanMutation.isPending ? (
                        <>
                          <Clock className="w-4 h-4 mr-2 animate-spin" />
                          Generating...
                        </>
                      ) : (
                        <>
                          <Sparkles className="w-4 h-4 mr-2" />
                          Generate Plan
                        </>
                      )}
                    </Button>
                  </form>
                </Form>
              </DialogContent>
            </Dialog>
          </div>

          {mealPlans.length === 0 ? (
            <Card>
              <CardContent className="text-center py-8">
                <p className="text-muted-foreground">No meal plans created yet</p>
                <p className="text-sm text-muted-foreground mt-2">Generate your first AI meal plan to get started</p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {mealPlans.map((plan) => (
                <Card key={plan.id} data-testid={`card-meal-plan-${plan.id}`}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="flex items-center gap-2">
                          {plan.isAIGenerated && <Sparkles className="w-4 h-4" />}
                          {plan.name}
                        </CardTitle>
                        <CardDescription>{plan.description}</CardDescription>
                      </div>
                      <Button
                        size="sm"
                        onClick={() => applyMealPlanMutation.mutate({ planId: plan.id, date: selectedDate })}
                        disabled={applyMealPlanMutation.isPending}
                        data-testid={`button-apply-plan-${plan.id}`}
                      >
                        <CheckCircle className="w-3 h-3 mr-1" />
                        Apply
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-4 gap-2 text-sm text-center">
                      <div>
                        <div className="font-medium">{plan.targetCalories}</div>
                        <div className="text-muted-foreground">calories</div>
                      </div>
                      <div>
                        <div className="font-medium">{plan.targetProtein}g</div>
                        <div className="text-muted-foreground">protein</div>
                      </div>
                      <div>
                        <div className="font-medium">{plan.targetCarbs}g</div>
                        <div className="text-muted-foreground">carbs</div>
                      </div>
                      <div>
                        <div className="font-medium">{plan.targetFat}g</div>
                        <div className="text-muted-foreground">fat</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        {/* ANALYTICS TAB */}
        <TabsContent value="analytics" className="space-y-6">
          <h2 className="text-xl font-semibold">Nutrition Analytics</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Daily Macros Breakdown */}
            <Card>
              <CardHeader>
                <CardTitle>Today's Macro Breakdown</CardTitle>
                <CardDescription>Calories from protein, carbs, and fat</CardDescription>
              </CardHeader>
              <CardContent>
                {macroData.reduce((sum, item) => sum + item.value, 0) === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    No nutrition data for today
                  </div>
                ) : (
                  <ResponsiveContainer width="100%" height={200}>
                    <PieChart>
                      <Pie
                        data={macroData}
                        cx="50%"
                        cy="50%"
                        innerRadius={60}
                        outerRadius={80}
                        paddingAngle={5}
                        dataKey="value"
                      >
                        {macroData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.fill} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value: number) => [`${Math.round(value)} cal`, 'Calories']} />
                    </PieChart>
                  </ResponsiveContainer>
                )}
              </CardContent>
            </Card>

            {/* Calorie Goal Progress */}
            <Card>
              <CardHeader>
                <CardTitle>Goal Progress</CardTitle>
                <CardDescription>Daily calorie and macro targets</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Calories</span>
                    <span>{Math.round(dailyNutrition.calories)} / {calorieTarget}</span>
                  </div>
                  <Progress value={(dailyNutrition.calories / calorieTarget) * 100} className="h-2" />
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Protein</span>
                    <span>{Math.round(dailyNutrition.protein)}g / {proteinTarget}g</span>
                  </div>
                  <Progress value={(dailyNutrition.protein / proteinTarget) * 100} className="h-2" />
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Carbs</span>
                    <span>{Math.round(dailyNutrition.carbs)}g / {carbTarget}g</span>
                  </div>
                  <Progress value={(dailyNutrition.carbs / carbTarget) * 100} className="h-2" />
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Fat</span>
                    <span>{Math.round(dailyNutrition.fat)}g / {fatTarget}g</span>
                  </div>
                  <Progress value={(dailyNutrition.fat / fatTarget) * 100} className="h-2" />
                </div>
              </CardContent>
            </Card>

            {/* Weekly Trends */}
            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="w-5 h-5" />
                  Weekly Trends
                </CardTitle>
                <CardDescription>Your nutrition progress over the past week</CardDescription>
              </CardHeader>
              <CardContent>
                {weeklyData.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    No weekly data available
                  </div>
                ) : (
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={weeklyData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" tick={{ fontSize: 12 }} />
                      <YAxis tick={{ fontSize: 12 }} />
                      <Tooltip />
                      <Line type="monotone" dataKey="totalCalories" stroke="#8884d8" strokeWidth={2} name="Calories" />
                      <Line type="monotone" dataKey="totalProtein" stroke="#82ca9d" strokeWidth={2} name="Protein (g)" />
                    </LineChart>
                  </ResponsiveContainer>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
      </div>
    </>
  );
}