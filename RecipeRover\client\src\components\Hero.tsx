import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { ArrowR<PERSON>, Users, TrendingUp, Zap } from "lucide-react";

export default function Hero() {
  return (
    <section className="relative pt-20 pb-32 px-4 lg:px-8" id="home">
      <div className="container mx-auto">
        {/* Main Hero Content */}
        <div className="text-center max-w-5xl mx-auto mb-16">
          <h1 className="text-5xl lg:text-7xl font-bold text-foreground mb-6 leading-tight">
            The Future of
            <br />
            <span className="text-primary">Personal Training</span>
          </h1>
          <p className="text-xl lg:text-2xl text-muted-foreground mb-8 max-w-3xl mx-auto leading-relaxed">
            Experience the perfect blend of expert human coaching and cutting-edge AI technology. 
            Achieve your fitness goals faster with personalized plans that adapt to your progress.
          </p>
          
          {/* Tagline */}
          <div className="inline-flex items-center gap-3 bg-primary/10 px-6 py-3 rounded-full mb-12">
            <span className="text-primary font-medium">Coach-led</span>
            <div className="w-2 h-2 bg-primary rounded-full"></div>
            <span className="text-primary font-medium">AI-assisted</span>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <Button size="lg" className="text-lg px-8 py-4" data-testid="button-start-journey">
              Start Your Journey
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
            <Button variant="outline" size="lg" className="text-lg px-8 py-4" data-testid="button-view-plans">
              View Plans
            </Button>
          </div>

          {/* Success Metric */}
          <div className="text-center">
            <div className="text-6xl font-bold text-primary mb-2">98%</div>
            <div className="text-lg text-muted-foreground">Success Rate</div>
          </div>
        </div>

        {/* Feature Cards */}
        <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
          <Card className="p-8 text-center hover-elevate">
            <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-6">
              <Users className="h-8 w-8 text-primary" />
            </div>
            <h3 className="text-xl font-semibold mb-4">Expert Human Trainers</h3>
            <p className="text-muted-foreground">
              Work with certified professionals who understand your unique fitness journey.
            </p>
          </Card>

          <Card className="p-8 text-center hover-elevate">
            <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-6">
              <Zap className="h-8 w-8 text-primary" />
            </div>
            <h3 className="text-xl font-semibold mb-4">AI-Powered Analytics</h3>
            <p className="text-muted-foreground">
              Advanced algorithms analyze your progress and optimize your workouts.
            </p>
          </Card>

          <Card className="p-8 text-center hover-elevate">
            <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-6">
              <TrendingUp className="h-8 w-8 text-primary" />
            </div>
            <h3 className="text-xl font-semibold mb-4">Personalized Plans</h3>
            <p className="text-muted-foreground">
              Custom fitness plans tailored to your goals, updated in real-time.
            </p>
          </Card>
        </div>
      </div>
    </section>
  );
}