#!/usr/bin/env node

/**
 * API Test Script for RecipeRover
 * Tests all major API endpoints to verify functionality
 */

const BASE_URL = 'http://localhost:5000/api';
const TEST_USER_ID = 'test-user-123';

async function makeRequest(method, endpoint, data = null) {
  const url = `${BASE_URL}${endpoint}`;
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
    },
  };
  
  if (data) {
    options.body = JSON.stringify(data);
  }
  
  try {
    const response = await fetch(url, options);
    const result = await response.json();
    return { status: response.status, data: result };
  } catch (error) {
    return { status: 500, error: error.message };
  }
}

async function testExercises() {
  console.log('\n🏋️ Testing Exercise API...');
  
  // Test get all exercises
  const allExercises = await makeRequest('GET', '/exercises');
  console.log(`✅ Get all exercises: ${allExercises.status} (${allExercises.data?.length || 0} exercises)`);
  
  // Test search exercises
  const searchResults = await makeRequest('GET', '/exercises?q=push');
  console.log(`✅ Search exercises: ${searchResults.status} (${searchResults.data?.length || 0} results)`);
  
  // Test get specific exercise
  if (allExercises.data && allExercises.data.length > 0) {
    const exerciseId = allExercises.data[0].id;
    const exercise = await makeRequest('GET', `/exercises/${exerciseId}`);
    console.log(`✅ Get specific exercise: ${exercise.status}`);
  }
}

async function testNutrition() {
  console.log('\n🍎 Testing Nutrition API...');
  
  // Test get all foods
  const allFoods = await makeRequest('GET', '/nutrition/foods');
  console.log(`✅ Get all foods: ${allFoods.status} (${allFoods.data?.length || 0} foods)`);
  
  // Test search foods
  const searchResults = await makeRequest('GET', '/nutrition/foods?q=chicken');
  console.log(`✅ Search foods: ${searchResults.status} (${searchResults.data?.length || 0} results)`);
}

async function testWorkouts() {
  console.log('\n💪 Testing Workout API...');
  
  // Test create workout session
  const workoutData = {
    userId: TEST_USER_ID,
    workoutType: 'strength',
    notes: 'Test workout session'
  };
  
  const createWorkout = await makeRequest('POST', '/workouts', workoutData);
  console.log(`✅ Create workout session: ${createWorkout.status}`);
  
  if (createWorkout.status === 200 && createWorkout.data) {
    const sessionId = createWorkout.data.id;
    
    // Test get workout session
    const getWorkout = await makeRequest('GET', `/workouts/${sessionId}`);
    console.log(`✅ Get workout session: ${getWorkout.status}`);
    
    // Test get user workouts
    const userWorkouts = await makeRequest('GET', `/workouts/user/${TEST_USER_ID}`);
    console.log(`✅ Get user workouts: ${userWorkouts.status} (${userWorkouts.data?.length || 0} sessions)`);
  }
}

async function testProfile() {
  console.log('\n👤 Testing Profile API...');
  
  // Test create user profile
  const profileData = {
    userId: TEST_USER_ID,
    age: 30,
    heightCm: 180,
    weightKg: 75,
    fitnessGoals: ['muscle_gain'],
    activityLevel: 'moderate',
    fitnessLevel: 'intermediate'
  };
  
  const createProfile = await makeRequest('POST', '/profile', profileData);
  console.log(`✅ Create user profile: ${createProfile.status}`);
  
  // Test get user profile
  const getProfile = await makeRequest('GET', `/profile/${TEST_USER_ID}`);
  console.log(`✅ Get user profile: ${getProfile.status}`);
}

async function testAnalytics() {
  console.log('\n📊 Testing Analytics API...');
  
  // Test analytics overview
  const overview = await makeRequest('GET', `/analytics/${TEST_USER_ID}/overview`);
  console.log(`✅ Get analytics overview: ${overview.status}`);
  
  // Test personal records
  const records = await makeRequest('GET', `/analytics/${TEST_USER_ID}/personal-records`);
  console.log(`✅ Get personal records: ${records.status}`);
}

async function runTests() {
  console.log('🚀 Starting RecipeRover API Tests...');
  console.log(`Testing against: ${BASE_URL}`);
  
  try {
    await testExercises();
    await testNutrition();
    await testWorkouts();
    await testProfile();
    await testAnalytics();
    
    console.log('\n✅ All tests completed successfully!');
    console.log('\n🎉 RecipeRover API is fully functional!');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error);
    process.exit(1);
  }
}

// Run tests
runTests();
