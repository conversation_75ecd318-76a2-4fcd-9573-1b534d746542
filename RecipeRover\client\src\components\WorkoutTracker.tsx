import { useState, useEffect, useCallback } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { apiRequest } from "@/lib/queryClient";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { useToast } from "@/hooks/use-toast";
import { 
  Play, 
  Pause, 
  Square, 
  Plus, 
  Minus, 
  Timer, 
  Camera, 
  CheckCircle2,
  <PERSON>,
  Dumbbell,
  Target,
  Activity
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import CameraCapture from "./CameraCapture";

// Shared user ID mechanism
import { getCurrentUserId } from "@/lib/currentUser";

// Form schemas
const startWorkoutSchema = z.object({
  workoutType: z.enum(["strength", "cardio", "flexibility", "mixed", "plyometric"]),
  notes: z.string().optional(),
});

const addExerciseSchema = z.object({
  exerciseName: z.string().min(1, "Exercise name is required"),
  targetSets: z.number().min(1).optional(),
});

const addSetSchema = z.object({
  reps: z.number().min(1, "Reps must be at least 1").optional(),
  weight: z.number().min(0, "Weight cannot be negative").optional(),
  restTime: z.number().min(0, "Rest time cannot be negative").optional(),
});

type StartWorkoutData = z.infer<typeof startWorkoutSchema>;
type AddExerciseData = z.infer<typeof addExerciseSchema>;
type AddSetData = z.infer<typeof addSetSchema>;

interface ExerciseSet {
  id: string;
  setNumber: number;
  reps?: number;
  weight?: number;
  restTime?: number;
  isCompleted: boolean;
  completedAt?: string;
  notes?: string;
}

interface SessionExercise {
  id: string;
  exerciseName: string;
  orderInSession: number;
  targetSets?: number;
  completedSets: number;
  notes?: string;
  sets: ExerciseSet[];
}

interface WorkoutSession {
  id: string;
  userId: string;
  startedAt: string;
  completedAt?: string;
  duration?: number;
  workoutType: string;
  notes?: string;
  isCompleted: boolean;
  exercises: SessionExercise[];
}

export default function WorkoutTracker() {
  const { toast } = useToast();
  const currentUserId = getCurrentUserId();
  const queryClient = useQueryClient();
  
  // State management
  const [sessionTimer, setSessionTimer] = useState(0);
  const [isTimerRunning, setIsTimerRunning] = useState(false);
  const [restTimer, setRestTimer] = useState(0);
  const [isRestTimerRunning, setIsRestTimerRunning] = useState(false);
  const [activeExerciseId, setActiveExerciseId] = useState<string | null>(null);
  const [showAddExercise, setShowAddExercise] = useState(false);
  const [showCamera, setShowCamera] = useState(false);
  const [selectedExerciseForCamera, setSelectedExerciseForCamera] = useState<string | null>(null);

  // Forms
  const startWorkoutForm = useForm<StartWorkoutData>({
    resolver: zodResolver(startWorkoutSchema),
    defaultValues: {
      workoutType: "strength",
      notes: "",
    },
  });

  const addExerciseForm = useForm<AddExerciseData>({
    resolver: zodResolver(addExerciseSchema),
    defaultValues: {
      exerciseName: "",
      targetSets: 3,
    },
  });

  const addSetForm = useForm<AddSetData>({
    resolver: zodResolver(addSetSchema),
    defaultValues: {
      reps: undefined,
      weight: undefined,
      restTime: 60,
    },
  });

  // Queries
  const { data: activeSession, refetch: refetchActiveSession } = useQuery<WorkoutSession | null>({
    queryKey: ['/api/workouts/active', currentUserId],
    enabled: true,
  });

  // Mutations
  const startWorkoutMutation = useMutation({
    mutationFn: async (data: StartWorkoutData) => {
      const response = await apiRequest('POST', `/api/workouts`, { ...data, userId: currentUserId });
      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/workouts/active', currentUserId] });
      setIsTimerRunning(true);
      toast({ title: "Workout started!", description: "Let's get moving!" });
    },
    onError: () => {
      toast({ title: "Error", description: "Failed to start workout", variant: "destructive" });
    },
  });

  const addExerciseMutation = useMutation({
    mutationFn: async ({ sessionId, data }: { sessionId: string; data: AddExerciseData }) => {
      const response = await apiRequest('POST', `/api/workouts/${sessionId}/exercises`, 
        { ...data, orderInSession: activeSession?.exercises.length || 0 });
      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/workouts/active', currentUserId] });
      setShowAddExercise(false);
      addExerciseForm.reset();
      toast({ title: "Exercise added!", description: "Ready to track your sets" });
    },
    onError: () => {
      toast({ title: "Error", description: "Failed to add exercise", variant: "destructive" });
    },
  });

  const addSetMutation = useMutation({
    mutationFn: async ({ exerciseId, data }: { exerciseId: string; data: AddSetData & { setNumber: number } }) => {
      const response = await apiRequest('POST', `/api/workouts/exercises/${exerciseId}/sets`, data);
      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/workouts/active', currentUserId] });
      addSetForm.reset({ reps: undefined, weight: undefined, restTime: 60 });
      toast({ title: "Set logged!", description: "Great job! Take a rest if needed." });
    },
    onError: () => {
      toast({ title: "Error", description: "Failed to log set", variant: "destructive" });
    },
  });

  const completeSetMutation = useMutation({
    mutationFn: async (setId: string) => {
      const response = await apiRequest('PATCH', `/api/workouts/sets/${setId}`, { isCompleted: true });
      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/workouts/active', currentUserId] });
      toast({ title: "Set completed!", description: "Way to go!" });
    },
  });

  const endWorkoutMutation = useMutation({
    mutationFn: async () => {
      if (!activeSession) throw new Error("No active session");
      const response = await apiRequest('POST', `/api/workouts/${activeSession.id}/complete`, {});
      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/workouts/active', currentUserId] });
      setIsTimerRunning(false);
      setSessionTimer(0);
      toast({ title: "Workout completed!", description: "Excellent work! Time to recover." });
    },
    onError: () => {
      toast({ title: "Error", description: "Failed to complete workout", variant: "destructive" });
    },
  });

  // Initialize timer from active session on mount
  useEffect(() => {
    if (activeSession && !activeSession.isCompleted) {
      const startTime = new Date(activeSession.startedAt).getTime();
      const currentTime = Date.now();
      const elapsedSeconds = Math.floor((currentTime - startTime) / 1000);
      
      setSessionTimer(elapsedSeconds);
      setIsTimerRunning(true);
    } else if (!activeSession) {
      setSessionTimer(0);
      setIsTimerRunning(false);
    }
  }, [activeSession]);

  // Timer effects
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isTimerRunning && activeSession) {
      interval = setInterval(() => {
        setSessionTimer((prev) => prev + 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isTimerRunning, activeSession]);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isRestTimerRunning && restTimer > 0) {
      interval = setInterval(() => {
        setRestTimer((prev) => {
          if (prev <= 1) {
            setIsRestTimerRunning(false);
            toast({ title: "Rest time over!", description: "Ready for your next set?" });
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isRestTimerRunning, restTimer, toast]);

  // Helper functions
  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const startRestTimer = (seconds: number) => {
    setRestTimer(seconds);
    setIsRestTimerRunning(true);
  };

  const handleStartWorkout = (data: StartWorkoutData) => {
    startWorkoutMutation.mutate(data);
  };

  const handleAddExercise = (data: AddExerciseData) => {
    if (!activeSession) return;
    addExerciseMutation.mutate({ sessionId: activeSession.id, data });
  };

  const handleAddSet = (exerciseId: string) => {
    const data = addSetForm.getValues();
    const exercise = activeSession?.exercises.find(e => e.id === exerciseId);
    if (!exercise) return;

    const setNumber = exercise.sets.length + 1;
    addSetMutation.mutate({ exerciseId, data: { ...data, setNumber } });
  };

  const openCameraForExercise = (exerciseName: string) => {
    setSelectedExerciseForCamera(exerciseName);
    setShowCamera(true);
  };

  // Camera capture component integration
  if (showCamera) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-2xl font-bold text-foreground">Form Analysis</h1>
              {selectedExerciseForCamera && (
                <p className="text-muted-foreground">Checking form for: {selectedExerciseForCamera}</p>
              )}
            </div>
            <Button 
              variant="outline" 
              onClick={() => setShowCamera(false)}
              data-testid="button-close-camera"
            >
              Back to Workout
            </Button>
          </div>
          
          <CameraCapture />
        </div>
      </div>
    );
  }

  // Main workout interface
  if (!activeSession) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-6">
          <div className="max-w-md mx-auto">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Dumbbell className="h-5 w-5 text-primary" />
                  Start New Workout
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Form {...startWorkoutForm}>
                  <form onSubmit={startWorkoutForm.handleSubmit(handleStartWorkout)} className="space-y-4">
                    <FormField
                      control={startWorkoutForm.control}
                      name="workoutType"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Workout Type</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger data-testid="select-workout-type">
                                <SelectValue placeholder="Choose workout type" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="strength">Strength Training</SelectItem>
                              <SelectItem value="cardio">Cardio</SelectItem>
                              <SelectItem value="flexibility">Flexibility</SelectItem>
                              <SelectItem value="mixed">Mixed</SelectItem>
                              <SelectItem value="plyometric">Plyometric</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={startWorkoutForm.control}
                      name="notes"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Notes (Optional)</FormLabel>
                          <FormControl>
                            <Textarea 
                              placeholder="Any notes about today's workout..."
                              {...field}
                              data-testid="textarea-workout-notes"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <Button 
                      type="submit" 
                      className="w-full" 
                      disabled={startWorkoutMutation.isPending}
                      data-testid="button-start-workout"
                    >
                      <Play className="h-4 w-4 mr-2" />
                      {startWorkoutMutation.isPending ? "Starting..." : "Start Workout"}
                    </Button>
                  </form>
                </Form>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  // Active workout interface
  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-6">
        {/* Workout Header */}
        <Card className="mb-6">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <Activity className="h-6 w-6 text-primary" />
                  <div>
                    <h1 className="text-2xl font-bold text-foreground" data-testid="text-workout-title">
                      {activeSession.workoutType.charAt(0).toUpperCase() + activeSession.workoutType.slice(1)} Workout
                    </h1>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Clock className="h-4 w-4" />
                      <span data-testid="text-session-timer">{formatTime(sessionTimer)}</span>
                      {isTimerRunning ? (
                        <Badge variant="secondary" className="ml-2">Active</Badge>
                      ) : (
                        <Badge variant="outline" className="ml-2">Paused</Badge>
                      )}
                    </div>
                  </div>
                </div>

                {/* Rest Timer */}
                {isRestTimerRunning && (
                  <div className="flex items-center gap-2 bg-muted px-3 py-2 rounded-lg">
                    <Timer className="h-4 w-4 text-primary" />
                    <span className="font-mono text-lg" data-testid="text-rest-timer">
                      {formatTime(restTimer)}
                    </span>
                    <Button 
                      size="sm" 
                      variant="outline" 
                      onClick={() => setIsRestTimerRunning(false)}
                      data-testid="button-stop-rest-timer"
                    >
                      Stop
                    </Button>
                  </div>
                )}
              </div>

              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  onClick={() => setIsTimerRunning(!isTimerRunning)}
                  data-testid="button-toggle-timer"
                >
                  {isTimerRunning ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                </Button>
                
                <Button
                  variant="destructive"
                  onClick={() => endWorkoutMutation.mutate()}
                  disabled={endWorkoutMutation.isPending}
                  data-testid="button-end-workout"
                >
                  <Square className="h-4 w-4 mr-2" />
                  End Workout
                </Button>
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Exercises */}
        <div className="space-y-4">
          {activeSession.exercises.map((exercise, index) => (
            <Card key={exercise.id} className="overflow-hidden">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary text-primary-foreground font-semibold">
                      {index + 1}
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold" data-testid={`text-exercise-name-${exercise.id}`}>
                        {exercise.exerciseName}
                      </h3>
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <span>Sets: {exercise.sets.length}</span>
                        <span>Completed: {exercise.sets.filter(s => s.isCompleted).length}</span>
                        {exercise.targetSets && <span>Target: {exercise.targetSets}</span>}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => openCameraForExercise(exercise.exerciseName)}
                      data-testid={`button-check-form-${exercise.id}`}
                    >
                      <Camera className="h-4 w-4 mr-1" />
                      Check Form
                    </Button>
                    
                    <Button
                      size="sm"
                      onClick={() => setActiveExerciseId(activeExerciseId === exercise.id ? null : exercise.id)}
                      data-testid={`button-toggle-exercise-${exercise.id}`}
                    >
                      {activeExerciseId === exercise.id ? "Close" : "Add Set"}
                    </Button>
                  </div>
                </div>
              </CardHeader>

              <CardContent>
                {/* Sets Display */}
                {exercise.sets.length > 0 && (
                  <div className="space-y-2 mb-4">
                    <Label>Sets</Label>
                    <div className="grid gap-2">
                      {exercise.sets.map((set) => (
                        <div 
                          key={set.id} 
                          className={`flex items-center justify-between p-3 rounded-lg border ${
                            set.isCompleted ? 'bg-muted border-primary' : 'bg-background'
                          }`}
                        >
                          <div className="flex items-center gap-4">
                            <span className="font-semibold">Set {set.setNumber}</span>
                            {set.reps && <span>{set.reps} reps</span>}
                            {set.weight && <span>{set.weight}kg</span>}
                          </div>
                          
                          <div className="flex items-center gap-2">
                            {set.restTime && !set.isCompleted && (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => startRestTimer(set.restTime!)}
                                data-testid={`button-start-rest-${set.id}`}
                              >
                                <Timer className="h-3 w-3 mr-1" />
                                Rest ({set.restTime}s)
                              </Button>
                            )}
                            
                            {!set.isCompleted ? (
                              <Button
                                size="sm"
                                onClick={() => completeSetMutation.mutate(set.id)}
                                disabled={completeSetMutation.isPending}
                                data-testid={`button-complete-set-${set.id}`}
                              >
                                <CheckCircle2 className="h-3 w-3 mr-1" />
                                Complete
                              </Button>
                            ) : (
                              <CheckCircle2 className="h-4 w-4 text-primary" />
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Add Set Form */}
                {activeExerciseId === exercise.id && (
                  <div className="border-t pt-4">
                    <Label className="mb-3 block">Log New Set</Label>
                    <Form {...addSetForm}>
                      <form onSubmit={addSetForm.handleSubmit(() => handleAddSet(exercise.id))} className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          <FormField
                            control={addSetForm.control}
                            name="reps"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Reps</FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    placeholder="12"
                                    {...field}
                                    onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : undefined)}
                                    data-testid={`input-reps-${exercise.id}`}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={addSetForm.control}
                            name="weight"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Weight (kg)</FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    step="0.5"
                                    placeholder="20"
                                    {...field}
                                    onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : undefined)}
                                    data-testid={`input-weight-${exercise.id}`}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        <FormField
                          control={addSetForm.control}
                          name="restTime"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Rest Time (seconds)</FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  placeholder="60"
                                  {...field}
                                  onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : undefined)}
                                  data-testid={`input-rest-time-${exercise.id}`}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <div className="flex gap-2">
                          <Button
                            type="submit"
                            disabled={addSetMutation.isPending}
                            data-testid={`button-log-set-${exercise.id}`}
                          >
                            Log Set
                          </Button>
                          <Button
                            type="button"
                            variant="outline"
                            onClick={() => setActiveExerciseId(null)}
                            data-testid={`button-cancel-set-${exercise.id}`}
                          >
                            Cancel
                          </Button>
                        </div>
                      </form>
                    </Form>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}

          {/* Add Exercise */}
          <Card>
            <CardContent className="pt-6">
              {!showAddExercise ? (
                <Button 
                  className="w-full" 
                  variant="outline"
                  onClick={() => setShowAddExercise(true)}
                  data-testid="button-show-add-exercise"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Exercise
                </Button>
              ) : (
                <div>
                  <Label className="mb-3 block">Add New Exercise</Label>
                  <Form {...addExerciseForm}>
                    <form onSubmit={addExerciseForm.handleSubmit(handleAddExercise)} className="space-y-4">
                      <FormField
                        control={addExerciseForm.control}
                        name="exerciseName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Exercise Name</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="e.g., Bench Press, Squats, Pull-ups"
                                {...field}
                                data-testid="input-exercise-name"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={addExerciseForm.control}
                        name="targetSets"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Target Sets (Optional)</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                placeholder="3"
                                {...field}
                                onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : undefined)}
                                data-testid="input-target-sets"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="flex gap-2">
                        <Button 
                          type="submit" 
                          disabled={addExerciseMutation.isPending}
                          data-testid="button-add-exercise-submit"
                        >
                          Add Exercise
                        </Button>
                        <Button 
                          type="button" 
                          variant="outline"
                          onClick={() => {
                            setShowAddExercise(false);
                            addExerciseForm.reset();
                          }}
                          data-testid="button-cancel-add-exercise"
                        >
                          Cancel
                        </Button>
                      </div>
                    </form>
                  </Form>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}