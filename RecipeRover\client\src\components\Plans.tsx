import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Clock, Users, Star, CheckCircle, Sparkles } from "lucide-react";

export default function Plans() {
  const plans = [
    {
      id: 1,
      title: "Beginner Full Body",
      description: "Perfect for those just starting their fitness journey",
      level: "Beginner",
      duration: "4 weeks",
      workoutsPerWeek: 3,
      rating: 4.9,
      features: ["Full body workouts", "Bodyweight exercises", "Progressive difficulty", "Video demonstrations"]
    },
    {
      id: 2,
      title: "Strength Builder Pro",
      description: "Advanced strength training for experienced lifters",
      level: "Advanced",
      duration: "8 weeks",
      workoutsPerWeek: 5,
      rating: 4.8,
      features: ["Compound movements", "Progressive overload", "Periodization", "1-on-1 coaching"]
    },
    {
      id: 3,
      title: "Fat Loss Accelerator",
      description: "High-intensity program designed for rapid fat loss",
      level: "Intermediate",
      duration: "6 weeks",
      workoutsPerWeek: 4,
      rating: 4.9,
      features: ["HIIT workouts", "Metabolic training", "Nutrition guidance", "Progress tracking"]
    }
  ];

  const selectPlan = (planId: number) => {
    console.log("Selected plan:", planId);
  };

  const getAIRecommendation = () => {
    console.log("Getting AI plan recommendation...");
  };

  return (
    <section className="py-24 px-4 lg:px-8 bg-muted/30" id="plans">
      <div className="container mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold mb-6">
            Fitness <span className="text-primary">Plans</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto mb-8">
            Expert-designed workout programs that adapt to your progress and goals.
          </p>
          
          {/* AI Recommendation CTA */}
          <Button 
            onClick={getAIRecommendation}
            size="lg" 
            className="mb-12"
            data-testid="button-ai-recommendation"
          >
            <Sparkles className="mr-2 h-5 w-5" />
            Get AI Plan Recommendation
          </Button>
        </div>

        {/* Plans Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {plans.map((plan) => (
            <Card key={plan.id} className="hover-elevate h-full flex flex-col">
              <CardHeader>
                <div className="flex items-start justify-between mb-2">
                  <Badge variant="secondary">{plan.level}</Badge>
                  <div className="flex items-center gap-1">
                    <Star className="h-4 w-4 fill-primary text-primary" />
                    <span className="text-sm font-medium">{plan.rating}</span>
                  </div>
                </div>
                <CardTitle className="text-xl">{plan.title}</CardTitle>
                <CardDescription>{plan.description}</CardDescription>
              </CardHeader>
              
              <CardContent className="flex-1 flex flex-col">
                {/* Plan Stats */}
                <div className="flex items-center gap-4 mb-6 text-sm text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    {plan.duration}
                  </div>
                  <div className="flex items-center gap-1">
                    <Users className="h-4 w-4" />
                    {plan.workoutsPerWeek}/week
                  </div>
                </div>

                {/* Features */}
                <div className="space-y-2 mb-6 flex-1">
                  {plan.features.map((feature, idx) => (
                    <div key={idx} className="flex items-center gap-2 text-sm">
                      <CheckCircle className="h-4 w-4 text-primary shrink-0" />
                      <span>{feature}</span>
                    </div>
                  ))}
                </div>

                {/* CTA Button */}
                <Button 
                  onClick={() => selectPlan(plan.id)}
                  className="w-full"
                  data-testid={`button-select-plan-${plan.id}`}
                >
                  Start This Plan
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Why Our Plans Work */}
        <Card className="bg-primary/5 border-primary/20">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl">Why Our Plans Work</CardTitle>
            <CardDescription>
              Data-driven approach ensures measurable progress and results
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-4 gap-6 text-center">
              <div>
                <div className="text-3xl font-bold text-primary mb-2">100+</div>
                <div className="text-sm text-muted-foreground">Expert-designed plans</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-primary mb-2">5,000+</div>
                <div className="text-sm text-muted-foreground">Successful transformations</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-primary mb-2">AI-Powered</div>
                <div className="text-sm text-muted-foreground">Adaptive programming</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-primary mb-2">24/7</div>
                <div className="text-sm text-muted-foreground">Support available</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
}