import { useState, useEffect, useRef, useCallback } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { apiRequest } from "@/lib/queryClient";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Form, FormControl, FormField, FormItem } from "@/components/ui/form";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useToast } from "@/hooks/use-toast";
import { 
  MessageCircle, 
  Send, 
  Sparkles, 
  Settings, 
  Target,
  Heart,
  Activity,
  Clock,
  User,
  Bot,
  Zap,
  TrendingUp,
  Play,
  HelpCircle,
  Lightbulb,
  AlertTriangle,
  Info,
  Shield,
  Wifi,
  WifiOff,
  Utensils,
  Apple,
  ChefHat
} from "lucide-react";
import { getCurrentUserId } from "@/lib/currentUser";

// Type definitions
interface ChatMessage {
  id: string;
  sender: 'user' | 'ai';
  content: string;
  messageType: 'text' | 'quick_action' | 'system';
  createdAt: string;
  metadata?: string;
}

interface ChatSession {
  id: string;
  userId: string;
  title: string;
  coachPersona: 'motivational' | 'technical' | 'supportive' | 'personal_trainer';
  context?: string;
  workoutSessionId?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  messages?: ChatMessage[];
}

interface CoachPersona {
  key: 'motivational' | 'technical' | 'supportive' | 'personal_trainer';
  name: string;
  description: string;
  icon: React.ReactNode;
  color: string;
}

// Form schemas
const messageSchema = z.object({
  content: z.string().min(1, "Message cannot be empty").max(2000, "Message too long (max 2000 characters)"),
});

const sessionSchema = z.object({
  coachPersona: z.enum(['motivational', 'technical', 'supportive', 'personal_trainer']),
  title: z.string().optional(),
  context: z.string().optional(),
});

type MessageFormData = z.infer<typeof messageSchema>;
type SessionFormData = z.infer<typeof sessionSchema>;

// Coach persona definitions
const coachPersonas: CoachPersona[] = [
  {
    key: 'motivational',
    name: 'Coach Alex',
    description: 'Energetic and encouraging, perfect for motivation and pushing through challenges',
    icon: <Zap className="w-4 h-4" />,
    color: 'bg-amber-500'
  },
  {
    key: 'technical',
    name: 'Coach Jordan',
    description: 'Detail-oriented and analytical, expert in form and technique',
    icon: <Target className="w-4 h-4" />,
    color: 'bg-blue-500'
  },
  {
    key: 'supportive',
    name: 'Coach Sam',
    description: 'Patient and understanding, great for beginners and emotional support',
    icon: <Heart className="w-4 h-4" />,
    color: 'bg-green-500'
  },
  {
    key: 'personal_trainer',
    name: 'Coach Taylor',
    description: 'Structured and results-focused, ideal for systematic training',
    icon: <TrendingUp className="w-4 h-4" />,
    color: 'bg-purple-500'
  }
];

// Quick actions
const quickActions = [
  { key: 'motivation', label: 'Motivate Me!', icon: <Zap className="w-4 h-4" />, color: 'bg-amber-100 text-amber-800 hover:bg-amber-200' },
  { key: 'form_help', label: 'Form Help', icon: <Target className="w-4 h-4" />, color: 'bg-blue-100 text-blue-800 hover:bg-blue-200' },
  { key: 'rest_time', label: 'Rest Time?', icon: <Clock className="w-4 h-4" />, color: 'bg-green-100 text-green-800 hover:bg-green-200' },
  { key: 'modify_exercise', label: 'Modify Exercise', icon: <Activity className="w-4 h-4" />, color: 'bg-purple-100 text-purple-800 hover:bg-purple-200' },
  { key: 'progress_check', label: 'Check Progress', icon: <TrendingUp className="w-4 h-4" />, color: 'bg-cyan-100 text-cyan-800 hover:bg-cyan-200' },
  { key: 'next_workout', label: 'Next Workout', icon: <Play className="w-4 h-4" />, color: 'bg-pink-100 text-pink-800 hover:bg-pink-200' },
  { key: 'plan_meals', label: 'Plan My Meals', icon: <ChefHat className="w-4 h-4" />, color: 'bg-emerald-100 text-emerald-800 hover:bg-emerald-200' },
  { key: 'nutrition_goals', label: 'Nutrition Goals', icon: <Apple className="w-4 h-4" />, color: 'bg-red-100 text-red-800 hover:bg-red-200' },
  { key: 'log_meals', label: 'Log Meals', icon: <Utensils className="w-4 h-4" />, color: 'bg-orange-100 text-orange-800 hover:bg-orange-200' },
];

interface AIChatCoachProps {
  workoutSessionId?: string;
  context?: any; // Additional context like form analysis, analytics, etc.
  className?: string;
  compact?: boolean; // For minimized view during workouts
}

export default function AIChatCoach({ 
  workoutSessionId, 
  context, 
  className = "", 
  compact = false 
}: AIChatCoachProps) {
  const userId = getCurrentUserId();
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // State management
  const [activeSessionId, setActiveSessionId] = useState<string | null>(null);
  const [isTyping, setIsTyping] = useState(false);
  const [showPersonaSelect, setShowPersonaSelect] = useState(false);
  const [selectedPersona, setSelectedPersona] = useState<CoachPersona>(coachPersonas[0]);

  // Form setup
  const messageForm = useForm<MessageFormData>({
    resolver: zodResolver(messageSchema),
    defaultValues: { content: "" }
  });

  // Auto-scroll to bottom of messages
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, []);

  // Get or create active session
  const { data: activeSession, isLoading: sessionLoading, error: sessionError } = useQuery({
    queryKey: ['chat', 'session', 'active', userId],
    queryFn: async () => {
      if (!userId) return null;
      
      // Try to get existing active session first
      const sessions = await fetch(`/api/chat/sessions/${userId}`).then(res => res.json());
      const existing = sessions.find((s: ChatSession) => s.isActive);
      
      if (existing) {
        setActiveSessionId(existing.id);
        const persona = coachPersonas.find(p => p.key === existing.coachPersona);
        if (persona) setSelectedPersona(persona);
        return existing;
      }

      // Create new session if none exists
      const newSession = await apiRequest('/api/chat/session', {
        method: 'POST',
        body: {
          userId,
          coachPersona: selectedPersona.key,
          title: `Chat with ${selectedPersona.name}`,
          context: context ? JSON.stringify(context) : undefined,
          workoutSessionId
        }
      });
      
      setActiveSessionId(newSession.id);
      return newSession;
    },
    enabled: !!userId,
    retry: 1
  });

  // Get session messages
  const { data: sessionWithMessages, isLoading: messagesLoading, refetch: refetchMessages } = useQuery({
    queryKey: ['chat', 'session', activeSessionId, 'messages'],
    queryFn: async () => {
      if (!activeSessionId) return null;
      const response = await fetch(`/api/chat/session/${activeSessionId}?limit=100`);
      return await response.json();
    },
    enabled: !!activeSessionId,
    refetchInterval: false
  });

  // Enhanced error message helper
  const getErrorMessage = (error: any): { title: string; description: string } => {
    if (error.message?.includes('Rate limit exceeded')) {
      return {
        title: "Too many messages",
        description: "Please wait a moment before sending another message."
      };
    }
    if (error.message?.includes('Message too long')) {
      return {
        title: "Message too long",
        description: "Please keep your message under 2000 characters."
      };
    }
    if (error.message?.includes('temporarily unavailable')) {
      return {
        title: "AI coach unavailable",
        description: "The AI coach is temporarily offline. Please try again in a few minutes."
      };
    }
    if (error.message?.includes('context_length_exceeded') || error.message?.includes('Conversation too long')) {
      return {
        title: "Conversation too long",
        description: "This conversation has reached its limit. Consider starting a new session."
      };
    }
    if (error.message?.includes('quota') || error.message?.includes('busy')) {
      return {
        title: "Service busy",
        description: "The AI coach is currently busy. Please try again in a few minutes."
      };
    }
    return {
      title: "Failed to send message",
      description: error.message || "Something went wrong. Please try again."
    };
  };

  // Send message mutation
  const sendMessageMutation = useMutation({
    mutationFn: async (data: { content: string; messageType?: string }) => {
      if (!activeSessionId) throw new Error('No active chat session. Please refresh the page.');
      
      setIsTyping(true);
      try {
        const response = await apiRequest('POST', '/api/chat/message', {
          sessionId: activeSessionId,
          sender: 'user',
          content: data.content,
          messageType: data.messageType || 'text'
        });
        
        return await response.json();
      } catch (error: any) {
        // Handle specific HTTP status codes
        if (error.status === 429) {
          throw new Error('Rate limit exceeded. Please wait before sending more messages.');
        }
        if (error.status === 503) {
          throw new Error('AI coach is temporarily unavailable. Please try again later.');
        }
        if (error.status === 400) {
          throw new Error(error.message || 'Invalid message format.');
        }
        throw error;
      }
    },
    onSuccess: () => {
      refetchMessages();
      messageForm.reset();
      setTimeout(scrollToBottom, 100);
    },
    onError: (error: Error) => {
      const errorInfo = getErrorMessage(error);
      toast({
        title: errorInfo.title,
        description: errorInfo.description,
        variant: "destructive",
      });
    },
    onSettled: () => {
      setIsTyping(false);
    }
  });

  // Meal planning mutation
  const generateMealPlanMutation = useMutation({
    mutationFn: async (prompt: string) => {
      const response = await apiRequest('/api/nutrition/plan/generate', {
        method: 'POST',
        body: {
          userId,
          prompt,
          mealsToInclude: ['breakfast', 'lunch', 'dinner']
        }
      });
      return response;
    },
    onSuccess: (data) => {
      // Add a message showing the meal plan was generated
      sendMessageMutation.mutate({
        content: `I've generated a meal plan for you! Here's what I created:\n\n**${data.name}**\n${data.description}\n\n📊 **Nutrition Summary:**\n- Calories: ${data.targetCalories}\n- Protein: ${data.targetProtein}g\n- Carbs: ${data.targetCarbs}g\n- Fat: ${data.targetFat}g\n\nYou can view the full meal plan in the Nutrition tab and apply it to your daily log!`,
        messageType: 'meal_plan_generated'
      });
      
      toast({
        title: "Meal plan generated!",
        description: "Check the Nutrition tab to view and apply your new meal plan.",
      });
    },
    onError: (error: any) => {
      const errorInfo = getErrorMessage(error);
      toast({
        title: errorInfo.title,
        description: errorInfo.description,
        variant: "destructive",
      });
    }
  });

  // Quick action mutation
  const quickActionMutation = useMutation({
    mutationFn: async (action: { key: string; data?: any }) => {
      if (!activeSessionId) throw new Error('No active chat session. Please refresh the page.');
      
      setIsTyping(true);
      
      // Handle nutrition-specific quick actions
      if (action.key === 'plan_meals') {
        // Generate a personalized meal plan
        generateMealPlanMutation.mutate('Create a balanced meal plan for today based on my nutrition goals and preferences');
        return { success: true };
      }
      
      if (action.key === 'nutrition_goals') {
        // Send a message about nutrition goals
        return await apiRequest('/api/chat/message', {
          method: 'POST',
          body: {
            sessionId: activeSessionId,
            sender: 'user',
            content: 'Tell me about my nutrition goals and how I\'m doing today',
            messageType: 'quick_action'
          }
        });
      }
      
      if (action.key === 'log_meals') {
        // Provide guidance on meal logging
        return await apiRequest('/api/chat/message', {
          method: 'POST',
          body: {
            sessionId: activeSessionId,
            sender: 'user',
            content: 'Help me log my meals for today and track my nutrition',
            messageType: 'quick_action'
          }
        });
      }
      
      // Handle regular quick actions
      try {
        return await apiRequest('/api/chat/quick-action', {
          method: 'POST',
          body: {
            sessionId: activeSessionId,
            action: action.key,
            data: action.data
          }
        });
      } catch (error: any) {
        if (error.status === 429) {
          throw new Error('Too many quick actions. Please wait a moment.');
        }
        if (error.status === 503) {
          throw new Error('AI coach is temporarily unavailable.');
        }
        throw error;
      }
    },
    onSuccess: () => {
      refetchMessages();
      setTimeout(scrollToBottom, 100);
    },
    onError: (error: Error) => {
      const errorInfo = getErrorMessage(error);
      toast({
        title: errorInfo.title,
        description: errorInfo.description,
        variant: "destructive",
      });
    },
    onSettled: () => {
      setIsTyping(false);
    }
  });

  // Change persona mutation
  const changePersonaMutation = useMutation({
    mutationFn: async (newPersona: CoachPersona) => {
      if (!activeSessionId) throw new Error('No active chat session. Please refresh the page.');
      
      const response = await apiRequest('PATCH', `/api/chat/session/${activeSessionId}`, {
        coachPersona: newPersona.key,
        title: `Chat with ${newPersona.name}`
      });
      
      return await response.json();
    },
    onSuccess: (data, variables) => {
      setSelectedPersona(variables);
      setShowPersonaSelect(false);
      refetchMessages();
      toast({
        title: "Coach changed",
        description: `Now chatting with ${variables.name}`,
      });
    },
    onError: (error: Error) => {
      const errorInfo = getErrorMessage(error);
      toast({
        title: errorInfo.title,
        description: errorInfo.description,
        variant: "destructive",
      });
    }
  });

  // Handle form submission
  const onSubmitMessage = (data: MessageFormData) => {
    if (!data.content.trim()) return;
    sendMessageMutation.mutate({ content: data.content.trim() });
  };

  // Handle quick action
  const handleQuickAction = (actionKey: string) => {
    quickActionMutation.mutate({ key: actionKey });
  };

  // Auto-scroll when new messages arrive
  useEffect(() => {
    if (sessionWithMessages?.messages) {
      setTimeout(scrollToBottom, 100);
    }
  }, [sessionWithMessages?.messages, scrollToBottom]);

  // Error state
  if (sessionError) {
    return (
      <Card className={`${className}`} data-testid="chat-error">
        <CardHeader className="pb-4">
          <div className="flex items-center gap-3 text-destructive">
            <WifiOff className="w-6 h-6" />
            <div className="flex-1">
              <CardTitle className="text-sm">Connection Error</CardTitle>
              <p className="text-xs text-muted-foreground mt-1">
                Unable to connect to AI coach. Please check your connection and try again.
              </p>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Button 
            onClick={() => window.location.reload()} 
            variant="outline" 
            size="sm"
            data-testid="button-reload"
          >
            Reload Page
          </Button>
        </CardContent>
      </Card>
    );
  }

  // Loading state
  if (sessionLoading || !activeSession) {
    return (
      <Card className={`${className} animate-pulse`} data-testid="chat-loading">
        <CardHeader className="pb-4">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-muted rounded-full" />
            <div className="flex-1">
              <div className="w-32 h-4 bg-muted rounded" />
              <div className="w-24 h-3 bg-muted rounded mt-1" />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="w-full h-10 bg-muted rounded" />
            <div className="w-3/4 h-8 bg-muted rounded ml-auto" />
            <div className="w-1/2 h-8 bg-muted rounded" />
          </div>
        </CardContent>
      </Card>
    );
  }

  const messages = sessionWithMessages?.messages || [];
  const currentPersona = coachPersonas.find(p => p.key === activeSession.coachPersona) || selectedPersona;

  // Safety Disclaimer Component
  const SafetyDisclaimer = () => (
    <Alert className="mb-4 border-amber-200 bg-amber-50 dark:border-amber-800 dark:bg-amber-900/20" data-testid="safety-disclaimer">
      <Shield className="h-4 w-4 text-amber-600 dark:text-amber-400" />
      <AlertDescription className="text-amber-800 dark:text-amber-200 text-sm">
        <strong>Important:</strong> This AI coach provides general fitness guidance only. It is not a substitute for professional medical advice, diagnosis, or treatment. 
        Always consult with qualified healthcare professionals before starting any new exercise program, especially if you have medical conditions or concerns.
      </AlertDescription>
    </Alert>
  );

  // Compact view for during workouts
  if (compact) {
    return (
      <Card className={`${className} max-h-96`} data-testid="chat-compact">
        <CardHeader className="pb-2 px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className={`w-6 h-6 rounded-full ${currentPersona.color} flex items-center justify-center text-white`}>
                {currentPersona.icon}
              </div>
              <span className="font-semibold text-sm">{currentPersona.name}</span>
            </div>
            <Badge variant="secondary" className="text-xs">
              <MessageCircle className="w-3 h-3 mr-1" />
              Quick Chat
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="px-4 py-2">
          {/* Quick Actions Only */}
          <div className="grid grid-cols-2 gap-2 mb-3">
            {quickActions.slice(0, 6).map((action) => (
              <Button
                key={action.key}
                variant="ghost"
                size="sm"
                className={`${action.color} text-xs h-8`}
                onClick={() => handleQuickAction(action.key)}
                disabled={quickActionMutation.isPending || generateMealPlanMutation.isPending}
                data-testid={`quick-action-${action.key}`}
              >
                {action.icon}
                <span className="ml-1 truncate">{action.label}</span>
              </Button>
            ))}
          </div>
          
          {/* Latest message */}
          {messages.length > 0 && (
            <div className="text-sm text-muted-foreground bg-muted/50 p-2 rounded-md">
              <div className="flex items-center gap-2 mb-1">
                <Bot className="w-3 h-3" />
                <span className="font-medium text-xs">{currentPersona.name}</span>
              </div>
              <p className="line-clamp-2">{messages[messages.length - 1]?.content}</p>
            </div>
          )}
          
          {isTyping && (
            <div className="flex items-center gap-2 mt-2 text-muted-foreground">
              <div className="flex gap-1">
                <div className="w-1 h-1 bg-current rounded-full animate-bounce" />
                <div className="w-1 h-1 bg-current rounded-full animate-bounce delay-100" />
                <div className="w-1 h-1 bg-current rounded-full animate-bounce delay-200" />
              </div>
              <span className="text-xs">{currentPersona.name} is typing...</span>
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  // Full chat interface
  return (
    <div>
      {/* Safety Disclaimer - Show for new sessions or periodically */}
      {(!sessionWithMessages?.messages || sessionWithMessages.messages.length === 0) && (
        <div className="mb-4">
          <SafetyDisclaimer />
        </div>
      )}
      
      <Card className={`${className} flex flex-col h-full max-h-[600px]`} data-testid="chat-full">
      {/* Header */}
      <CardHeader className="pb-4 border-b">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className={`w-10 h-10 rounded-full ${currentPersona.color} flex items-center justify-center text-white`}>
              {currentPersona.icon}
            </div>
            <div>
              <CardTitle className="text-lg">{currentPersona.name}</CardTitle>
              <p className="text-sm text-muted-foreground">{currentPersona.description}</p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setShowPersonaSelect(!showPersonaSelect)}
            data-testid="button-change-persona"
          >
            <Settings className="w-4 h-4" />
          </Button>
        </div>

        {/* Persona selector */}
        {showPersonaSelect && (
          <div className="mt-4 grid grid-cols-2 gap-2">
            {coachPersonas.map((persona) => (
              <Button
                key={persona.key}
                variant={persona.key === currentPersona.key ? "default" : "outline"}
                className="h-auto p-3 flex flex-col items-start gap-1"
                onClick={() => changePersonaMutation.mutate(persona)}
                disabled={changePersonaMutation.isPending}
                data-testid={`persona-${persona.key}`}
              >
                <div className="flex items-center gap-2 w-full">
                  <div className={`w-6 h-6 rounded-full ${persona.color} flex items-center justify-center text-white`}>
                    {persona.icon}
                  </div>
                  <span className="font-semibold text-sm">{persona.name}</span>
                </div>
                <span className="text-xs text-left opacity-80">{persona.description}</span>
              </Button>
            ))}
          </div>
        )}
      </CardHeader>

      {/* Messages */}
      <CardContent className="flex-1 p-0 overflow-hidden">
        <ScrollArea className="h-full px-6 py-4" data-testid="chat-messages">
          <div className="space-y-4">
            {messages.length === 0 && (
              <div className="text-center py-8">
                <Sparkles className="w-12 h-12 mx-auto text-muted-foreground mb-3" />
                <h3 className="font-semibold mb-2">Welcome to AI Coaching!</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  I'm {currentPersona.name}, ready to help you achieve your fitness goals.
                </p>
                <div className="flex flex-wrap gap-2 justify-center">
                  {quickActions.slice(0, 6).map((action) => (
                    <Badge 
                      key={action.key} 
                      variant="secondary" 
                      className="cursor-pointer hover:bg-secondary/80"
                      onClick={() => handleQuickAction(action.key)}
                    >
                      {action.icon}
                      <span className="ml-1">{action.label}</span>
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex gap-3 ${message.sender === 'user' ? 'flex-row-reverse' : ''}`}
                data-testid={`message-${message.sender}-${message.id}`}
              >
                {/* Avatar */}
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white flex-shrink-0 ${
                  message.sender === 'user' 
                    ? 'bg-primary' 
                    : currentPersona.color
                }`}>
                  {message.sender === 'user' ? <User className="w-4 h-4" /> : currentPersona.icon}
                </div>

                {/* Message bubble */}
                <div className={`max-w-[80%] ${message.sender === 'user' ? 'text-right' : ''}`}>
                  <div className={`inline-block p-3 rounded-lg ${
                    message.sender === 'user'
                      ? 'bg-primary text-primary-foreground'
                      : 'bg-muted'
                  }`}>
                    <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                  </div>
                  <p className="text-xs text-muted-foreground mt-1 px-1">
                    {new Date(message.createdAt).toLocaleTimeString([], { 
                      hour: '2-digit', 
                      minute: '2-digit' 
                    })}
                  </p>
                </div>
              </div>
            ))}

            {/* Typing indicator */}
            {isTyping && (
              <div className="flex gap-3">
                <div className={`w-8 h-8 rounded-full ${currentPersona.color} flex items-center justify-center text-white`}>
                  {currentPersona.icon}
                </div>
                <div className="bg-muted p-3 rounded-lg">
                  <div className="flex gap-1">
                    <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" />
                    <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce delay-100" />
                    <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce delay-200" />
                  </div>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>
      </CardContent>

      {/* Input area */}
      <div className="border-t p-4 space-y-3">
        {/* Quick Actions */}
        <div className="flex flex-wrap gap-2">
          {quickActions.map((action) => (
            <Button
              key={action.key}
              variant="ghost"
              size="sm"
              className={`${action.color} text-xs h-7`}
              onClick={() => handleQuickAction(action.key)}
              disabled={quickActionMutation.isPending}
              data-testid={`quick-action-${action.key}`}
            >
              {action.icon}
              <span className="ml-1">{action.label}</span>
            </Button>
          ))}
        </div>

        {/* Message input */}
        <Form {...messageForm}>
          <form 
            onSubmit={messageForm.handleSubmit(onSubmitMessage)}
            className="flex gap-2"
            data-testid="chat-form"
          >
            <FormField
              control={messageForm.control}
              name="content"
              render={({ field }) => (
                <FormItem className="flex-1">
                  <FormControl>
                    <Input
                      {...field}
                      placeholder={`Ask ${currentPersona.name} anything...`}
                      disabled={sendMessageMutation.isPending || isTyping}
                      className="resize-none"
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' && !e.shiftKey) {
                          e.preventDefault();
                          messageForm.handleSubmit(onSubmitMessage)();
                        }
                      }}
                      data-testid="input-message"
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <Button
              type="submit"
              disabled={sendMessageMutation.isPending || isTyping || !messageForm.watch('content')?.trim()}
              data-testid="button-send"
            >
              <Send className="w-4 h-4" />
            </Button>
          </form>
        </Form>
      </div>
    </Card>
    </div>
  );
}