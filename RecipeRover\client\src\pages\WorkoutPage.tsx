import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import WorkoutTracker from "@/components/WorkoutTracker";
import AI<PERSON>hatCoach from "@/components/AIChatCoach";
import Header from "@/components/Header";
import { getCurrentUserId } from "@/lib/currentUser";
import { Button } from "@/components/ui/button";
import { MessageCircle, X } from "lucide-react";

export default function WorkoutPage() {
  const userId = getCurrentUserId();
  const [showChat, setShowChat] = useState(false);
  const [chatContext, setChatContext] = useState<any>(null);

  // Get active workout session for chat context
  const { data: activeWorkout } = useQuery({
    queryKey: ['workouts', 'active', userId],
    queryFn: async () => {
      if (!userId) return null;
      const response = await fetch(`/api/workouts/active/${userId}`);
      return response.ok ? await response.json() : null;
    },
    enabled: !!userId,
    refetchInterval: 30000, // Refresh every 30 seconds during workout
  });

  // Update chat context when workout changes
  useEffect(() => {
    if (activeWorkout) {
      setChatContext({
        workout: {
          id: activeWorkout.id,
          type: activeWorkout.workoutType,
          startedAt: activeWorkout.startedAt,
          exercises: activeWorkout.exercises || []
        }
      });
    }
  }, [activeWorkout]);

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* SEO Meta tags */}
      <title>Workout Tracker - Circuit Fitness</title>
      <meta name="description" content="Track your workout sessions with AI-powered form analysis and real-time coaching. Log exercises, sets, reps, and weight with AI guidance." />
      
      <main className="pt-6">
        <div className="container px-4 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main workout tracker */}
            <div className="lg:col-span-2">
              <WorkoutTracker />
            </div>
            
            {/* Chat sidebar for desktop or overlay for mobile */}
            <div className="lg:col-span-1">
              <div className="sticky top-20">
                {activeWorkout ? (
                  <div className="mb-4">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-semibold">AI Coach</h3>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setShowChat(!showChat)}
                        data-testid="toggle-chat"
                      >
                        {showChat ? <X className="w-4 h-4" /> : <MessageCircle className="w-4 h-4" />}
                        {showChat ? 'Hide' : 'Chat'}
                      </Button>
                    </div>
                    
                    {showChat && (
                      <AIChatCoach
                        workoutSessionId={activeWorkout.id}
                        context={chatContext}
                        compact={true}
                        className="max-h-96"
                        data-testid="workout-chat"
                      />
                    )}
                  </div>
                ) : (
                  <div className="text-center p-6 border-2 border-dashed border-muted rounded-lg">
                    <MessageCircle className="w-8 h-8 mx-auto text-muted-foreground mb-2" />
                    <p className="text-sm text-muted-foreground mb-3">
                      Start a workout to get real-time AI coaching
                    </p>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowChat(true)}
                    >
                      Chat with AI Coach
                    </Button>
                    {showChat && (
                      <div className="mt-4">
                        <AIChatCoach className="max-h-80" />
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}