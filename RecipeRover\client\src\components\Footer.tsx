import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Mail, Twitter, Instagram, Linkedin } from "lucide-react";
import circuitLogo from "@assets/ChatGPT Image Sep 14, 2025, 03_07_53 PM_1758219151827.png";

export default function Footer() {
  const subscribeToNewsletter = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Newsletter subscription submitted");
  };

  return (
    <footer className="bg-muted/30 border-t py-16 px-4 lg:px-8">
      <div className="container mx-auto">
        {/* Main Footer Content */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {/* Brand */}
          <div className="lg:col-span-1">
            <div className="flex items-center space-x-3 mb-4">
              <img src={circuitLogo} alt="Circuit" className="h-8 w-8" />
              <div className="flex flex-col">
                <span className="text-xl font-bold text-foreground">CIRCUIT</span>
                <span className="text-xs text-muted-foreground">Coach-led • AI-assisted</span>
              </div>
            </div>
            <p className="text-sm text-muted-foreground mb-6">
              The perfect blend of expert human coaching and cutting-edge AI technology 
              for your fitness journey.
            </p>
            
            {/* Social Links */}
            <div className="flex space-x-4">
              <Button variant="ghost" size="icon" data-testid="link-twitter">
                <Twitter className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="icon" data-testid="link-instagram">
                <Instagram className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="icon" data-testid="link-linkedin">
                <Linkedin className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Features */}
          <div>
            <h3 className="font-semibold text-foreground mb-4">Features</h3>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li><a href="#ai-tools" className="hover:text-primary transition-colors">AI Coach Chat</a></li>
              <li><a href="#ai-tools" className="hover:text-primary transition-colors">Meal Planner</a></li>
              <li><a href="#ai-tools" className="hover:text-primary transition-colors">Workout Builder</a></li>
              <li><a href="#progress" className="hover:text-primary transition-colors">Progress Tracking</a></li>
            </ul>
          </div>

          {/* Company */}
          <div>
            <h3 className="font-semibold text-foreground mb-4">Company</h3>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li><a href="#" className="hover:text-primary transition-colors">About Us</a></li>
              <li><a href="#" className="hover:text-primary transition-colors">Our Trainers</a></li>
              <li><a href="#" className="hover:text-primary transition-colors">Success Stories</a></li>
              <li><a href="#" className="hover:text-primary transition-colors">Contact</a></li>
            </ul>
          </div>

          {/* Newsletter */}
          <div>
            <h3 className="font-semibold text-foreground mb-4">Stay Updated</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Get the latest fitness tips and product updates.
            </p>
            <form onSubmit={subscribeToNewsletter} className="space-y-2">
              <Input
                type="email"
                placeholder="Enter your email"
                className="text-sm"
                data-testid="input-newsletter-email"
              />
              <Button type="submit" className="w-full" data-testid="button-subscribe">
                <Mail className="mr-2 h-4 w-4" />
                Subscribe
              </Button>
            </form>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center py-12 border-t border-b">
          <h3 className="text-2xl font-bold mb-4">Ready to Transform Your Fitness?</h3>
          <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
            Join thousands of satisfied clients who have achieved their fitness goals 
            with our innovative approach.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" data-testid="button-get-started-footer">
              Get Started Today
            </Button>
            <Button variant="outline" size="lg" data-testid="button-meet-trainers">
              Meet Our Trainers
            </Button>
          </div>
        </div>

        {/* Bottom Footer */}
        <div className="flex flex-col md:flex-row justify-between items-center pt-8 text-sm text-muted-foreground">
          <div>
            © 2024 Circuit. All rights reserved.
          </div>
          <div className="flex space-x-6 mt-4 md:mt-0">
            <a href="#" className="hover:text-primary transition-colors">Privacy Policy</a>
            <a href="#" className="hover:text-primary transition-colors">Terms of Service</a>
            <a href="#" className="hover:text-primary transition-colors">Cookie Policy</a>
          </div>
        </div>
      </div>
    </footer>
  );
}