import { Router } from 'express';
import OpenAI from 'openai';

const router = Router();

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Request size validation for image uploads
function validateImageSize(req: any, res: any, next: any) {
  // Check if request body size exceeds reasonable limits for images
  const requestSize = JSON.stringify(req.body).length;
  const maxImageSize = 10 * 1024 * 1024; // 10MB limit for images
  
  if (requestSize > maxImageSize) {
    return res.status(413).json({ 
      error: 'Image too large. Please compress your image to under 10MB.' 
    });
  }
  
  next();
}

// Analyze exercise form from image/video frame
router.post('/analyze-form', validateImageSize, async (req, res) => {
  try {
    const { image, exercise, prompt } = req.body;

    if (!image) {
      return res.status(400).json({ error: 'Image is required' });
    }

    if (!process.env.OPENAI_API_KEY) {
      return res.status(500).json({ error: 'OpenAI API key not configured' });
    }

    const defaultPrompt = prompt || `Analyze this exercise form for ${exercise || 'the exercise shown'}. Provide specific feedback on:
    1. Posture and alignment
    2. Any form corrections needed  
    3. Safety concerns
    4. Overall form rating (1-10)
    5. Specific areas for improvement
    
    Be encouraging but honest about form issues. You MUST respond with valid JSON only - no markdown, no code fences, no additional text. Format as:
    {
      "rating": number_between_1_and_10,
      "feedback": "encouraging_feedback_string",
      "corrections": ["correction1", "correction2"],
      "safetyNotes": ["safety1", "safety2"]
    }`;

    const response = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      response_format: { type: "json_object" },
      messages: [
        {
          role: "user",
          content: [
            { type: "text", text: defaultPrompt },
            {
              type: "image_url",
              image_url: {
                url: image,
              },
            },
          ],
        },
      ],
      max_tokens: 500,
    });

    const analysis = response.choices[0]?.message?.content;
    
    if (!analysis) {
      return res.status(500).json({ error: 'No analysis received from AI model' });
    }
    
    try {
      // Clean and parse JSON response
      let cleanedAnalysis = analysis.trim();
      
      // Remove markdown code fences if present
      if (cleanedAnalysis.startsWith('```json')) {
        cleanedAnalysis = cleanedAnalysis.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      } else if (cleanedAnalysis.startsWith('```')) {
        cleanedAnalysis = cleanedAnalysis.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }
      
      const jsonAnalysis = JSON.parse(cleanedAnalysis);
      
      // Validate required fields
      const validatedResult = {
        rating: typeof jsonAnalysis.rating === 'number' ? 
          Math.max(1, Math.min(10, jsonAnalysis.rating)) : 5,
        feedback: typeof jsonAnalysis.feedback === 'string' ? 
          jsonAnalysis.feedback : 'Analysis completed',
        corrections: Array.isArray(jsonAnalysis.corrections) ? 
          jsonAnalysis.corrections : [],
        safetyNotes: Array.isArray(jsonAnalysis.safetyNotes) ? 
          jsonAnalysis.safetyNotes : []
      };
      
      res.json(validatedResult);
    } catch (parseError) {
      console.error('JSON parsing error:', parseError);
      return res.status(500).json({ error: 'Failed to parse AI response' });
    }

  } catch (error) {
    console.error('Vision analysis error:', error);
    res.status(500).json({ error: 'Failed to analyze form' });
  }
});

// Compare progress photos
router.post('/compare-progress', validateImageSize, async (req, res) => {
  try {
    const { beforeImage, afterImage, timeframe } = req.body;

    if (!beforeImage || !afterImage) {
      return res.status(400).json({ error: 'Both before and after images are required' });
    }

    const prompt = `Compare these two progress photos taken ${timeframe || 'over time'}. Analyze:
    1. Visible changes in muscle definition
    2. Body composition changes
    3. Posture improvements
    4. Overall transformation progress
    5. Areas of most notable improvement
    
    Be encouraging and specific about positive changes. You MUST respond with valid JSON only - no markdown, no code fences, no additional text. Format as:
    {
      "overallProgress": "detailed_encouraging_assessment_string",
      "muscleChanges": ["change1", "change2"],
      "postureChanges": ["improvement1", "improvement2"],
      "recommendations": ["recommendation1", "recommendation2"]
    }`;

    const response = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      response_format: { type: "json_object" },
      messages: [
        {
          role: "user",
          content: [
            { type: "text", text: prompt },
            {
              type: "image_url",
              image_url: { url: beforeImage },
            },
            {
              type: "image_url", 
              image_url: { url: afterImage },
            },
          ],
        },
      ],
      max_tokens: 400,
    });

    const analysis = response.choices[0]?.message?.content;
    
    if (!analysis) {
      return res.status(500).json({ error: 'No comparison analysis received from AI model' });
    }
    
    try {
      // Clean and parse JSON response
      let cleanedAnalysis = analysis.trim();
      
      // Remove markdown code fences if present
      if (cleanedAnalysis.startsWith('```json')) {
        cleanedAnalysis = cleanedAnalysis.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      } else if (cleanedAnalysis.startsWith('```')) {
        cleanedAnalysis = cleanedAnalysis.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }
      
      const jsonAnalysis = JSON.parse(cleanedAnalysis);
      
      // Validate required fields
      const validatedResult = {
        overallProgress: typeof jsonAnalysis.overallProgress === 'string' ? 
          jsonAnalysis.overallProgress : 'Progress analysis completed',
        muscleChanges: Array.isArray(jsonAnalysis.muscleChanges) ? 
          jsonAnalysis.muscleChanges : [],
        postureChanges: Array.isArray(jsonAnalysis.postureChanges) ? 
          jsonAnalysis.postureChanges : [],
        recommendations: Array.isArray(jsonAnalysis.recommendations) ? 
          jsonAnalysis.recommendations : []
      };
      
      res.json(validatedResult);
    } catch (parseError) {
      console.error('JSON parsing error:', parseError);
      return res.status(500).json({ error: 'Failed to parse AI comparison response' });
    }

  } catch (error) {
    console.error('Progress comparison error:', error);
    res.status(500).json({ error: 'Failed to compare progress photos' });
  }
});

export default router;