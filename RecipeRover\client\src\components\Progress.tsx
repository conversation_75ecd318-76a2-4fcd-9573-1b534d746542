import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { TrendingUp, Target, Calendar, Plus } from "lucide-react";
import { useState } from "react";

export default function Progress() {
  const [weight, setWeight] = useState("");
  const [notes, setNotes] = useState("");

  // Mock progress data
  const progressData = [
    { date: 'Jan', weight: 180, workouts: 12 },
    { date: 'Feb', weight: 178, workouts: 15 },
    { date: 'Mar', weight: 175, workouts: 18 },
    { date: 'Apr', weight: 172, workouts: 20 },
    { date: 'May', weight: 170, workouts: 22 },
    { date: 'Jun', weight: 168, workouts: 25 }
  ];

  const addProgressEntry = () => {
    console.log("Adding progress entry:", { weight, notes, date: new Date().toISOString() });
    setWeight("");
    setNotes("");
  };

  return (
    <section className="py-24 px-4 lg:px-8" id="progress">
      <div className="container mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold mb-6">
            Track Your <span className="text-primary">Progress</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Comprehensive analytics and visualizations to monitor your improvements 
            and celebrate milestones.
          </p>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Progress Chart */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-primary" />
                Weight Progress
              </CardTitle>
              <CardDescription>Your weight loss journey over the past 6 months</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-80 w-full">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={progressData}>
                    <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                    <XAxis dataKey="date" className="text-muted-foreground" />
                    <YAxis className="text-muted-foreground" />
                    <Tooltip 
                      contentStyle={{ 
                        backgroundColor: 'hsl(var(--card))',
                        border: '1px solid hsl(var(--border))',
                        borderRadius: '6px'
                      }}
                    />
                    <Line 
                      type="monotone" 
                      dataKey="weight" 
                      stroke="hsl(var(--primary))" 
                      strokeWidth={3}
                      dot={{ fill: 'hsl(var(--primary))', strokeWidth: 2, r: 6 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          {/* Add Progress Entry */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Plus className="h-5 w-5 text-primary" />
                  Log Progress
                </CardTitle>
                <CardDescription>Add today's measurements and notes</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="weight">Weight (lbs)</Label>
                  <Input
                    id="weight"
                    type="number"
                    placeholder="170"
                    value={weight}
                    onChange={(e) => setWeight(e.target.value)}
                    data-testid="input-weight"
                  />
                </div>
                <div>
                  <Label htmlFor="notes">Notes</Label>
                  <Input
                    id="notes"
                    placeholder="Feeling strong today!"
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    data-testid="input-notes"
                  />
                </div>
                <Button onClick={addProgressEntry} className="w-full" data-testid="button-add-progress">
                  Add Entry
                </Button>
              </CardContent>
            </Card>

            {/* Stats Cards */}
            <div className="grid grid-cols-2 gap-4">
              <Card className="p-4 text-center">
                <div className="text-2xl font-bold text-primary mb-1">-12</div>
                <div className="text-sm text-muted-foreground">lbs lost</div>
              </Card>
              <Card className="p-4 text-center">
                <div className="text-2xl font-bold text-primary mb-1">112</div>
                <div className="text-sm text-muted-foreground">workouts</div>
              </Card>
              <Card className="p-4 text-center">
                <div className="text-2xl font-bold text-primary mb-1">6</div>
                <div className="text-sm text-muted-foreground">months</div>
              </Card>
              <Card className="p-4 text-center">
                <div className="text-2xl font-bold text-primary mb-1">98%</div>
                <div className="text-sm text-muted-foreground">adherence</div>
              </Card>
            </div>
          </div>
        </div>

        {/* AI Insights */}
        <Card className="mt-8 bg-primary/5 border-primary/20">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5 text-primary" />
              AI Progress Insights
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-6">
              <div>
                <h4 className="font-semibold text-primary mb-2">Trending Well</h4>
                <p className="text-sm text-muted-foreground">
                  You're losing weight at a healthy rate of 2 lbs per month. Keep up the excellent work!
                </p>
              </div>
              <div>
                <h4 className="font-semibold text-primary mb-2">Next Week Focus</h4>
                <p className="text-sm text-muted-foreground">
                  Increase protein intake by 20g daily to support your strength training progress.
                </p>
              </div>
              <div>
                <h4 className="font-semibold text-primary mb-2">Milestone Alert</h4>
                <p className="text-sm text-muted-foreground">
                  You're only 3 lbs away from your next goal! Stay consistent with your current routine.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
}