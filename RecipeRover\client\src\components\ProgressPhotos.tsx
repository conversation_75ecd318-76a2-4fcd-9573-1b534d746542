import { useState, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Camera, Upload, RotateCcw, Loader2, TrendingUp, AlertCircle } from 'lucide-react';

interface ProgressComparison {
  overallProgress: string;
  muscleChanges: string[];
  postureChanges: string[];
  recommendations: string[];
}

export default function ProgressPhotos() {
  const [beforeImage, setBeforeImage] = useState<string>('');
  const [afterImage, setAfterImage] = useState<string>('');
  const [timeframe, setTimeframe] = useState('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [comparison, setComparison] = useState<ProgressComparison | null>(null);
  const [error, setError] = useState<string>('');
  
  const beforeFileRef = useRef<HTMLInputElement>(null);
  const afterFileRef = useRef<HTMLInputElement>(null);

  const handleImageUpload = (file: File, setImage: (data: string) => void) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const imageData = e.target?.result as string;
      setImage(imageData);
    };
    reader.readAsDataURL(file);
  };

  const compareProgress = async () => {
    if (!beforeImage || !afterImage) {
      console.log('Both images required for comparison');
      return;
    }

    setIsAnalyzing(true);
    setError('');
    
    try {
      const response = await fetch('/api/vision/compare-progress', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          beforeImage,
          afterImage,
          timeframe,
        }),
      });

      if (!response.ok) {
        throw new Error('Comparison failed');
      }

      const result = await response.json();
      setComparison(result);
      console.log('Progress comparison result:', result);
    } catch (error) {
      console.error('Progress comparison error:', error);
      setError('Failed to compare progress photos. Please try again.');
      setComparison(null);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const resetComparison = () => {
    setBeforeImage('');
    setAfterImage('');
    setTimeframe('');
    setComparison(null);
    if (beforeFileRef.current) beforeFileRef.current.value = '';
    if (afterFileRef.current) afterFileRef.current.value = '';
  };

  return (
    <section className="py-12 px-4 lg:px-8" id="progress-photos">
      <div className="container mx-auto max-w-6xl">
        <div className="text-center mb-8">
          <h2 className="text-3xl lg:text-4xl font-bold mb-4">
            Progress Photo <span className="text-primary">Comparison</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Upload before and after photos to get AI-powered analysis of your transformation progress.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-8">
          {/* Image Upload Section */}
          <div className="space-y-6">
            {/* Before Photo */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Before Photo</CardTitle>
                <CardDescription>Upload your starting transformation photo</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {beforeImage ? (
                    <div className="relative">
                      <img 
                        src={beforeImage} 
                        alt="Before" 
                        className="w-full h-48 object-cover rounded-lg"
                      />
                      <Button
                        variant="ghost"
                        size="icon"
                        className="absolute top-2 right-2 bg-background/80"
                        onClick={() => setBeforeImage('')}
                        data-testid="button-remove-before"
                      >
                        <RotateCcw className="h-4 w-4" />
                      </Button>
                    </div>
                  ) : (
                    <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center">
                      <Camera className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <p className="text-sm text-muted-foreground mb-4">
                        Upload your before photo
                      </p>
                      <Input
                        ref={beforeFileRef}
                        type="file"
                        accept="image/*"
                        onChange={(e) => {
                          const file = e.target.files?.[0];
                          if (file) handleImageUpload(file, setBeforeImage);
                        }}
                        className="hidden"
                        data-testid="input-before-image"
                      />
                      <Button 
                        onClick={() => beforeFileRef.current?.click()}
                        data-testid="button-upload-before"
                      >
                        <Upload className="mr-2 h-4 w-4" />
                        Choose File
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* After Photo */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">After Photo</CardTitle>
                <CardDescription>Upload your current transformation photo</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {afterImage ? (
                    <div className="relative">
                      <img 
                        src={afterImage} 
                        alt="After" 
                        className="w-full h-48 object-cover rounded-lg"
                      />
                      <Button
                        variant="ghost"
                        size="icon"
                        className="absolute top-2 right-2 bg-background/80"
                        onClick={() => setAfterImage('')}
                        data-testid="button-remove-after"
                      >
                        <RotateCcw className="h-4 w-4" />
                      </Button>
                    </div>
                  ) : (
                    <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center">
                      <Camera className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <p className="text-sm text-muted-foreground mb-4">
                        Upload your after photo
                      </p>
                      <Input
                        ref={afterFileRef}
                        type="file"
                        accept="image/*"
                        onChange={(e) => {
                          const file = e.target.files?.[0];
                          if (file) handleImageUpload(file, setAfterImage);
                        }}
                        className="hidden"
                        data-testid="input-after-image"
                      />
                      <Button 
                        onClick={() => afterFileRef.current?.click()}
                        data-testid="button-upload-after"
                      >
                        <Upload className="mr-2 h-4 w-4" />
                        Choose File
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Timeframe Input */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Timeframe</CardTitle>
                <CardDescription>How long between these photos?</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="timeframe">Duration</Label>
                    <Input
                      id="timeframe"
                      placeholder="e.g., 3 months, 6 weeks, 1 year"
                      value={timeframe}
                      onChange={(e) => setTimeframe(e.target.value)}
                      data-testid="input-timeframe"
                    />
                  </div>
                  
                  <Button 
                    onClick={compareProgress}
                    disabled={!beforeImage || !afterImage || isAnalyzing}
                    className="w-full"
                    data-testid="button-compare-progress"
                  >
                    {isAnalyzing ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Analyzing...
                      </>
                    ) : (
                      <>
                        <TrendingUp className="mr-2 h-4 w-4" />
                        Compare Progress
                      </>
                    )}
                  </Button>
                  
                  {(beforeImage || afterImage) && (
                    <Button 
                      onClick={resetComparison}
                      variant="outline"
                      className="w-full"
                      data-testid="button-reset-comparison"
                    >
                      <RotateCcw className="mr-2 h-4 w-4" />
                      Reset Photos
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Analysis Results */}
          <div className="space-y-6">
            {error && (
              <Card className="border-destructive/20 bg-destructive/5">
                <CardContent className="flex items-center justify-center py-12">
                  <div className="text-center">
                    <AlertCircle className="h-8 w-8 text-destructive mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2 text-destructive">Comparison Failed</h3>
                    <p className="text-muted-foreground mb-4">{error}</p>
                    <Button onClick={() => setError('')} variant="outline">
                      Try Again
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {comparison && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5 text-primary" />
                    Progress Analysis
                  </CardTitle>
                  <CardDescription>AI assessment of your transformation</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Overall Progress */}
                  <div>
                    <h4 className="font-semibold mb-3">Overall Assessment</h4>
                    <p className="text-sm text-muted-foreground bg-green-500/10 border border-green-500/20 p-4 rounded-lg">
                      {comparison.overallProgress}
                    </p>
                  </div>

                  {/* Muscle Changes */}
                  {comparison.muscleChanges && comparison.muscleChanges.length > 0 && (
                    <div>
                      <h4 className="font-semibold mb-3 flex items-center gap-2">
                        <Badge variant="secondary">Muscle Development</Badge>
                      </h4>
                      <ul className="space-y-2">
                        {comparison.muscleChanges.map((change, idx) => (
                          <li key={idx} className="text-sm flex items-start gap-2">
                            <div className="w-2 h-2 bg-primary rounded-full mt-2 shrink-0"></div>
                            <span>{change}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {/* Posture Changes */}
                  {comparison.postureChanges && comparison.postureChanges.length > 0 && (
                    <div>
                      <h4 className="font-semibold mb-3 flex items-center gap-2">
                        <Badge variant="secondary">Posture Improvements</Badge>
                      </h4>
                      <ul className="space-y-2">
                        {comparison.postureChanges.map((change, idx) => (
                          <li key={idx} className="text-sm flex items-start gap-2">
                            <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 shrink-0"></div>
                            <span>{change}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {/* Recommendations */}
                  {comparison.recommendations && comparison.recommendations.length > 0 && (
                    <div>
                      <h4 className="font-semibold mb-3 flex items-center gap-2">
                        <Badge variant="outline">Next Steps</Badge>
                      </h4>
                      <ul className="space-y-2">
                        {comparison.recommendations.map((rec, idx) => (
                          <li key={idx} className="text-sm flex items-start gap-2">
                            <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2 shrink-0"></div>
                            <span>{rec}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {!comparison && !isAnalyzing && (
              <Card className="border-dashed">
                <CardContent className="flex items-center justify-center py-12">
                  <div className="text-center">
                    <TrendingUp className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">Ready for Comparison</h3>
                    <p className="text-muted-foreground">
                      Upload both photos to see your AI-powered progress analysis
                    </p>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </section>
  );
}