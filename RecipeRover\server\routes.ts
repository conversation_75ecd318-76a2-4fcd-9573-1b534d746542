import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import visionRoutes from "./routes/vision";
import { 
  insertWorkoutSessionSchema, 
  insertSessionExerciseSchema, 
  insertExerciseSetSchema,
  updateWorkoutSessionSchema,
  updateSessionExerciseSchema,
  insertUserProfileSchema,
  updateUserProfileSchema,
  insertChatSessionSchema,
  updateChatSessionSchema,
  insertChatMessageSchema,
  insertFoodSchema,
  insertFoodPortionSchema,
  insertMealSchema,
  updateMealSchema,
  insertMealItemSchema,
  updateMealItemSchema,
  insertMealPlanSchema,
  insertMealPlanItemSchema
} from "@shared/schema";
import { z } from "zod";
import OpenAI from 'openai';

// Rate limiting storage (in-memory for demo)
interface RateLimit {
  requests: number;
  resetTime: number;
}

const rateLimits = new Map<string, RateLimit>();

// Rate limiting middleware for chat endpoints
function chatRateLimit(maxRequests: number = 20, windowMs: number = 60000) {
  return (req: any, res: any, next: any) => {
    const userId = req.body.userId || req.params.userId || req.headers['x-user-id'] || 'anonymous';
    const now = Date.now();
    const userLimit = rateLimits.get(userId);
    
    if (!userLimit || now > userLimit.resetTime) {
      rateLimits.set(userId, { requests: 1, resetTime: now + windowMs });
      return next();
    }
    
    if (userLimit.requests >= maxRequests) {
      return res.status(429).json({ 
        error: 'Rate limit exceeded. Please wait before sending more messages.',
        resetTime: new Date(userLimit.resetTime).toISOString()
      });
    }
    
    userLimit.requests++;
    next();
  };
}

// Token counting helper for conversation management
function estimateTokens(text: string): number {
  // Rough estimation: ~4 characters per token for English text
  return Math.ceil(text.length / 4);
}

// Conversation windowing to manage context length
function manageConversationWindow(messages: any[], maxTokens: number = 3500): any[] {
  let totalTokens = 0;
  const managedMessages = [];
  
  // Process messages from newest to oldest
  for (let i = messages.length - 1; i >= 0; i--) {
    const message = messages[i];
    const messageTokens = estimateTokens(message.content);
    
    if (totalTokens + messageTokens > maxTokens && managedMessages.length > 0) {
      break; // Stop adding messages if we exceed token limit
    }
    
    managedMessages.unshift(message);
    totalTokens += messageTokens;
  }
  
  return managedMessages;
}

// Enhanced error logging for production monitoring
function logCriticalError(context: string, error: any, additionalData?: any) {
  const timestamp = new Date().toISOString();
  const logData = {
    timestamp,
    context,
    error: {
      message: error.message,
      stack: error.stack,
      code: error.code,
      status: error.status,
    },
    additionalData: additionalData || {}
  };
  
  // Log to console for development, in production this could be sent to monitoring service
  console.error(`[CRITICAL ERROR ${timestamp}] ${context}:`, JSON.stringify(logData, null, 2));
}

// Enhanced OpenAI error handling
function handleOpenAIError(error: any) {
  // Log critical errors for production monitoring
  logCriticalError('OpenAI API', error, { 
    endpoint: 'chat_completion',
    model: 'gpt-4o-mini'
  });
  
  if (error.code === 'insufficient_quota') {
    return { status: 503, message: 'AI coach is temporarily unavailable due to quota limits. Please try again later.' };
  }
  
  if (error.code === 'rate_limit_exceeded') {
    return { status: 429, message: 'AI coach is busy. Please wait a moment before trying again.' };
  }
  
  if (error.code === 'context_length_exceeded') {
    return { status: 400, message: 'Conversation too long. Starting a new session might help.' };
  }
  
  if (error.status === 401) {
    return { status: 500, message: 'AI coach configuration error. Please contact support.' };
  }
  
  return { status: 500, message: 'AI coach encountered an error. Please try again.' };
}

// Helper function to calculate profile completion percentage
function calculateProfileCompletion(profile: any): number {
  const requiredFields = ['age', 'height', 'weight', 'fitnessLevel', 'goals', 'activityLevel'];
  const optionalFields = ['targetWeight', 'targetDate', 'preferredWorkoutDuration', 'weeklyWorkoutFrequency'];
  
  let completedRequired = 0;
  let completedOptional = 0;
  
  // Check required fields
  requiredFields.forEach(field => {
    if (profile[field] !== null && profile[field] !== undefined && 
        (Array.isArray(profile[field]) ? profile[field].length > 0 : true)) {
      completedRequired++;
    }
  });
  
  // Check optional fields
  optionalFields.forEach(field => {
    if (profile[field] !== null && profile[field] !== undefined &&
        (Array.isArray(profile[field]) ? profile[field].length > 0 : true)) {
      completedOptional++;
    }
  });
  
  // Weight required fields more heavily (60%) vs optional (40%)
  const requiredWeight = 0.6;
  const optionalWeight = 0.4;
  
  const requiredPercentage = (completedRequired / requiredFields.length) * requiredWeight;
  const optionalPercentage = (completedOptional / optionalFields.length) * optionalWeight;
  
  return Math.round((requiredPercentage + optionalPercentage) * 100);
}

export async function registerRoutes(app: Express): Promise<Server> {
  // Vision API routes
  app.use('/api/vision', visionRoutes);

  // User profile routes
  
  // Get user profile
  app.get('/api/profile/:userId', async (req, res) => {
    try {
      const profile = await storage.getUserProfile(req.params.userId);
      if (!profile) {
        return res.status(404).json({ error: 'User profile not found' });
      }
      res.json(profile);
    } catch (error) {
      res.status(500).json({ error: 'Failed to fetch user profile' });
    }
  });

  // Create user profile
  app.post('/api/profile', async (req, res) => {
    try {
      const validatedData = insertUserProfileSchema.parse(req.body);
      
      // Check if profile already exists for this user
      const existingProfile = await storage.getUserProfile(validatedData.userId);
      if (existingProfile) {
        return res.status(400).json({ error: 'User profile already exists' });
      }
      
      const profile = await storage.createUserProfile(validatedData);
      res.json(profile);
    } catch (error) {
      res.status(400).json({ error: error instanceof Error ? error.message : 'Invalid data' });
    }
  });

  // Update user profile
  app.patch('/api/profile/:userId', async (req, res) => {
    try {
      const validatedData = updateUserProfileSchema.parse(req.body);
      const profile = await storage.updateUserProfile(req.params.userId, validatedData);
      if (!profile) {
        return res.status(404).json({ error: 'User profile not found' });
      }
      res.json(profile);
    } catch (error) {
      res.status(400).json({ error: error instanceof Error ? error.message : 'Invalid data' });
    }
  });

  // Delete user profile (optional - for cleanup)
  app.delete('/api/profile/:userId', async (req, res) => {
    try {
      const deleted = await storage.deleteUserProfile(req.params.userId);
      if (!deleted) {
        return res.status(404).json({ error: 'User profile not found' });
      }
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ error: 'Failed to delete user profile' });
    }
  });

  // Check profile completion status
  app.get('/api/profile/:userId/status', async (req, res) => {
    try {
      const profile = await storage.getUserProfile(req.params.userId);
      if (!profile) {
        return res.json({ exists: false, isComplete: false });
      }
      res.json({ 
        exists: true, 
        isComplete: profile.isComplete,
        completionPercentage: calculateProfileCompletion(profile)
      });
    } catch (error) {
      res.status(500).json({ error: 'Failed to check profile status' });
    }
  });

  // Workout session routes
  
  // Create new workout session
  app.post('/api/workouts', async (req, res) => {
    try {
      const validatedData = insertWorkoutSessionSchema.parse(req.body);
      const session = await storage.createWorkoutSession(validatedData);
      res.json(session);
    } catch (error) {
      res.status(400).json({ error: error instanceof Error ? error.message : 'Invalid data' });
    }
  });

  // Get specific workout session
  app.get('/api/workouts/:id', async (req, res) => {
    try {
      const session = await storage.getWorkoutSession(req.params.id);
      if (!session) {
        return res.status(404).json({ error: 'Workout session not found' });
      }
      
      // Get session exercises and their sets
      const exercises = await storage.getSessionExercises(session.id);
      const exercisesWithSets = await Promise.all(
        exercises.map(async (exercise) => {
          const sets = await storage.getExerciseSets(exercise.id);
          return { ...exercise, sets };
        })
      );
      
      res.json({ ...session, exercises: exercisesWithSets });
    } catch (error) {
      res.status(500).json({ error: 'Failed to fetch workout session' });
    }
  });

  // Complete workout session
  app.post('/api/workouts/:id/complete', async (req, res) => {
    try {
      const session = await storage.getWorkoutSession(req.params.id);
      if (!session) {
        return res.status(404).json({ error: 'Workout session not found' });
      }
      
      if (session.isCompleted) {
        return res.status(400).json({ error: 'Workout session is already completed' });
      }
      
      const startTime = new Date(session.startedAt).getTime();
      const endTime = Date.now();
      const duration = Math.floor((endTime - startTime) / 1000); // Duration in seconds
      
      const updatedSession = await storage.updateWorkoutSession(req.params.id, {
        isCompleted: true,
        completedAt: new Date(),
        duration
      });
      
      res.json(updatedSession);
    } catch (error) {
      res.status(500).json({ error: 'Failed to complete workout session' });
    }
  });

  // Update workout session
  app.patch('/api/workouts/:id', async (req, res) => {
    try {
      const validatedData = updateWorkoutSessionSchema.parse(req.body);
      const session = await storage.updateWorkoutSession(req.params.id, validatedData);
      if (!session) {
        return res.status(404).json({ error: 'Workout session not found' });
      }
      res.json(session);
    } catch (error) {
      res.status(400).json({ error: error instanceof Error ? error.message : 'Invalid data' });
    }
  });

  // Get user's workout sessions
  app.get('/api/workouts/user/:userId', async (req, res) => {
    try {
      const sessions = await storage.getUserWorkoutSessions(req.params.userId);
      res.json(sessions);
    } catch (error) {
      res.status(500).json({ error: 'Failed to fetch user workout sessions' });
    }
  });

  // Get active workout session for user
  app.get('/api/workouts/active/:userId', async (req, res) => {
    try {
      const session = await storage.getActiveWorkoutSession(req.params.userId);
      if (!session) {
        return res.json(null);
      }
      
      // Get session exercises and their sets
      const exercises = await storage.getSessionExercises(session.id);
      const exercisesWithSets = await Promise.all(
        exercises.map(async (exercise) => {
          const sets = await storage.getExerciseSets(exercise.id);
          return { ...exercise, sets };
        })
      );
      
      res.json({ ...session, exercises: exercisesWithSets });
    } catch (error) {
      res.status(500).json({ error: 'Failed to fetch active workout session' });
    }
  });

  // Session exercise routes

  // Add exercise to session
  app.post('/api/workouts/:sessionId/exercises', async (req, res) => {
    try {
      const exerciseData = {
        ...req.body,
        sessionId: req.params.sessionId,
      };
      const validatedData = insertSessionExerciseSchema.parse(exerciseData);
      const exercise = await storage.addExerciseToSession(validatedData);
      res.json(exercise);
    } catch (error) {
      res.status(400).json({ error: error instanceof Error ? error.message : 'Invalid data' });
    }
  });

  // Get session exercises
  app.get('/api/workouts/:sessionId/exercises', async (req, res) => {
    try {
      const exercises = await storage.getSessionExercises(req.params.sessionId);
      const exercisesWithSets = await Promise.all(
        exercises.map(async (exercise) => {
          const sets = await storage.getExerciseSets(exercise.id);
          return { ...exercise, sets };
        })
      );
      res.json(exercisesWithSets);
    } catch (error) {
      res.status(500).json({ error: 'Failed to fetch session exercises' });
    }
  });

  // Update session exercise
  app.patch('/api/workouts/exercises/:id', async (req, res) => {
    try {
      const validatedData = updateSessionExerciseSchema.parse(req.body);
      const exercise = await storage.updateSessionExercise(req.params.id, validatedData);
      if (!exercise) {
        return res.status(404).json({ error: 'Session exercise not found' });
      }
      res.json(exercise);
    } catch (error) {
      res.status(400).json({ error: error instanceof Error ? error.message : 'Invalid data' });
    }
  });

  // Delete session exercise
  app.delete('/api/workouts/exercises/:id', async (req, res) => {
    try {
      const deleted = await storage.deleteSessionExercise(req.params.id);
      if (!deleted) {
        return res.status(404).json({ error: 'Session exercise not found' });
      }
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ error: 'Failed to delete session exercise' });
    }
  });

  // Exercise set routes

  // Add set to exercise
  app.post('/api/workouts/exercises/:exerciseId/sets', async (req, res) => {
    try {
      const setData = {
        ...req.body,
        sessionExerciseId: req.params.exerciseId,
      };
      const validatedData = insertExerciseSetSchema.parse(setData);
      const set = await storage.addExerciseSet(validatedData);
      res.json(set);
    } catch (error) {
      res.status(400).json({ error: error instanceof Error ? error.message : 'Invalid data' });
    }
  });

  // Get exercise sets
  app.get('/api/workouts/exercises/:exerciseId/sets', async (req, res) => {
    try {
      const sets = await storage.getExerciseSets(req.params.exerciseId);
      res.json(sets);
    } catch (error) {
      res.status(500).json({ error: 'Failed to fetch exercise sets' });
    }
  });

  // Update exercise set
  app.patch('/api/workouts/sets/:id', async (req, res) => {
    try {
      const set = await storage.updateExerciseSet(req.params.id, req.body);
      if (!set) {
        return res.status(404).json({ error: 'Exercise set not found' });
      }
      res.json(set);
    } catch (error) {
      res.status(400).json({ error: error instanceof Error ? error.message : 'Invalid data' });
    }
  });

  // Delete exercise set
  app.delete('/api/workouts/sets/:id', async (req, res) => {
    try {
      const deleted = await storage.deleteExerciseSet(req.params.id);
      if (!deleted) {
        return res.status(404).json({ error: 'Exercise set not found' });
      }
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ error: 'Failed to delete exercise set' });
    }
  });

  // Analytics routes

  // Validation schemas for analytics queries
  const analyticsQuerySchema = z.object({
    startDate: z.string().optional(),
    endDate: z.string().optional(),
    exercise: z.string().optional(),
    groupBy: z.enum(['day', 'week', 'month']).optional(),
    limit: z.coerce.number().positive().optional()
  });

  // Get analytics overview
  app.get('/api/analytics/:userId/overview', async (req, res) => {
    try {
      const { startDate, endDate } = analyticsQuerySchema.parse(req.query);
      
      const dateRange = startDate && endDate ? {
        startDate: new Date(startDate),
        endDate: new Date(endDate)
      } : undefined;

      const overview = await storage.getAnalyticsOverview(req.params.userId, dateRange);
      res.json(overview);
    } catch (error) {
      res.status(400).json({ error: error instanceof Error ? error.message : 'Invalid query parameters' });
    }
  });

  // Get workout trends
  app.get('/api/analytics/:userId/trends', async (req, res) => {
    try {
      const { startDate, endDate, groupBy } = analyticsQuerySchema.parse(req.query);
      
      if (!startDate || !endDate) {
        return res.status(400).json({ error: 'startDate and endDate are required for trends' });
      }

      const dateRange = {
        startDate: new Date(startDate),
        endDate: new Date(endDate)
      };

      const trends = await storage.getWorkoutTrends(req.params.userId, dateRange, groupBy || 'week');
      res.json(trends);
    } catch (error) {
      res.status(400).json({ error: error instanceof Error ? error.message : 'Invalid query parameters' });
    }
  });

  // Get volume progression
  app.get('/api/analytics/:userId/volume-progression', async (req, res) => {
    try {
      const { startDate, endDate, exercise } = analyticsQuerySchema.parse(req.query);
      
      const dateRange = startDate && endDate ? {
        startDate: new Date(startDate),
        endDate: new Date(endDate)
      } : undefined;

      const progression = await storage.getVolumeProgression(req.params.userId, exercise, dateRange);
      res.json(progression);
    } catch (error) {
      res.status(400).json({ error: error instanceof Error ? error.message : 'Invalid query parameters' });
    }
  });

  // Get personal records
  app.get('/api/analytics/:userId/personal-records', async (req, res) => {
    try {
      const { exercise, limit } = analyticsQuerySchema.parse(req.query);
      
      const records = await storage.getPersonalRecords(req.params.userId, exercise, limit);
      res.json(records);
    } catch (error) {
      res.status(400).json({ error: error instanceof Error ? error.message : 'Invalid query parameters' });
    }
  });

  // Get muscle group distribution
  app.get('/api/analytics/:userId/muscle-groups', async (req, res) => {
    try {
      const { startDate, endDate } = analyticsQuerySchema.parse(req.query);
      
      const dateRange = startDate && endDate ? {
        startDate: new Date(startDate),
        endDate: new Date(endDate)
      } : undefined;

      const distribution = await storage.getMuscleGroupDistribution(req.params.userId, dateRange);
      res.json(distribution);
    } catch (error) {
      res.status(400).json({ error: error instanceof Error ? error.message : 'Invalid query parameters' });
    }
  });

  // Get goal progress
  app.get('/api/analytics/:userId/goals', async (req, res) => {
    try {
      const progress = await storage.getGoalProgress(req.params.userId);
      res.json(progress);
    } catch (error) {
      res.status(500).json({ error: 'Failed to fetch goal progress' });
    }
  });

  // Get achievements
  app.get('/api/analytics/:userId/achievements', async (req, res) => {
    try {
      const { limit } = analyticsQuerySchema.parse(req.query);
      
      const achievements = await storage.getAchievements(req.params.userId, limit);
      res.json(achievements);
    } catch (error) {
      res.status(400).json({ error: error instanceof Error ? error.message : 'Invalid query parameters' });
    }
  });

  // Initialize OpenAI client
  const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
  });

  // Coaching personas with specialized prompts
  const coachingPersonas = {
    motivational: {
      name: "Coach Alex",
      personality: "energetic, encouraging, positive, uses motivational language",
      specialties: "motivation, goal setting, mindset coaching, overcoming plateaus",
      tone: "enthusiastic and supportive, uses exclamation points, celebrates victories",
      approach: "Focus on building confidence, celebrating progress, and pushing through challenges"
    },
    technical: {
      name: "Coach Jordan", 
      personality: "analytical, detail-oriented, form-focused, scientific",
      specialties: "exercise form, biomechanics, technique analysis, injury prevention",
      tone: "professional and precise, uses technical terms appropriately, fact-based",
      approach: "Focus on proper form, movement patterns, and evidence-based training principles"
    },
    supportive: {
      name: "Coach Sam",
      personality: "patient, gentle, understanding, empathetic",
      specialties: "beginner guidance, anxiety management, gentle progression, emotional support",
      tone: "calm and reassuring, uses gentle language, validates feelings",
      approach: "Focus on gradual progress, building confidence, and creating a safe space"
    },
    personal_trainer: {
      name: "Coach Taylor",
      personality: "structured, goal-oriented, disciplined, results-focused",
      specialties: "workout planning, progress tracking, performance optimization, accountability",
      tone: "direct and professional, uses action-oriented language, data-driven",
      approach: "Focus on systematic progression, measurable results, and structured programming"
    }
  };

  // Helper function to build context-aware prompts
  async function buildCoachingPrompt(userId: string, coachPersona: keyof typeof coachingPersonas, message: string, context?: any) {
    const persona = coachingPersonas[coachPersona];
    const profile = await storage.getUserProfile(userId);
    const activeWorkout = await storage.getActiveWorkoutSession(userId);
    
    // Parse additional context if provided
    let contextData = {};
    try {
      contextData = context ? JSON.parse(context) : {};
    } catch (error) {
      console.warn('Failed to parse context data:', error);
    }

    let systemPrompt = `You are ${persona.name}, an expert fitness coach with the following characteristics:
- Personality: ${persona.personality}
- Specialties: ${persona.specialties}
- Tone: ${persona.tone}
- Approach: ${persona.approach}

You are helping a user achieve their fitness goals through personalized coaching, motivation, and expert guidance.`;

    // Add user profile context
    if (profile) {
      systemPrompt += `\n\nUser Profile:
- Fitness Level: ${profile.fitnessLevel}
- Goals: ${profile.goals.join(', ')}
- Age: ${profile.age || 'Not specified'}
- Activity Level: ${profile.activityLevel}
- Preferred Workout Duration: ${profile.preferredWorkoutDuration} minutes
- Weekly Frequency: ${profile.weeklyWorkoutFrequency} times per week`;

      if (profile.availableEquipment && profile.availableEquipment.length > 0) {
        systemPrompt += `\n- Available Equipment: ${profile.availableEquipment.join(', ')}`;
      }

      if (profile.weight && profile.height) {
        systemPrompt += `\n- Height: ${profile.height}cm, Weight: ${profile.weight}kg`;
      }

      if (profile.targetWeight) {
        systemPrompt += `\n- Target Weight: ${profile.targetWeight}kg`;
      }
    }

    // Add active workout context
    if (activeWorkout) {
      systemPrompt += `\n\nCurrent Workout Session:
- Type: ${activeWorkout.workoutType}
- Started: ${activeWorkout.startedAt}
- Status: In Progress`;

      // Add exercise details if available
      const exercises = await storage.getSessionExercises(activeWorkout.id);
      if (exercises.length > 0) {
        systemPrompt += `\n- Exercises: ${exercises.map(e => `${e.exerciseName} (${e.completedSets}/${e.targetSets || 'open'} sets)`).join(', ')}`;
      }
    }

    // Add form analysis context if available
    if (contextData.formAnalysis) {
      systemPrompt += `\n\nRecent Form Analysis:
- Exercise: ${contextData.formAnalysis.exercise || 'Unknown'}
- Rating: ${contextData.formAnalysis.rating || 'N/A'}/10
- Feedback: ${contextData.formAnalysis.feedback || ''}`;
      
      if (contextData.formAnalysis.corrections && contextData.formAnalysis.corrections.length > 0) {
        systemPrompt += `\n- Suggested Corrections: ${contextData.formAnalysis.corrections.join(', ')}`;
      }
    }

    // Add analytics context if available
    if (contextData.analytics) {
      systemPrompt += `\n\nRecent Progress:
- Total Workouts: ${contextData.analytics.totalWorkouts || 0}
- Current Streak: ${contextData.analytics.currentStreak || 0} days
- Consistency: ${contextData.analytics.workoutConsistency || 0}%`;
    }

    systemPrompt += `\n\nIMPORTANT: Always respond in character as ${persona.name}. Keep responses concise, actionable, and tailored to the user's specific situation. If they ask about exercise form, provide specific technique cues. If they need motivation, be encouraging and positive. Always prioritize safety and proper progression.`;

    return systemPrompt;
  }

  // AI Chat Routes

  // Create or get active chat session
  app.post('/api/chat/session', chatRateLimit(10, 60000), async (req, res) => {
    try {
      // Validate input data
      const sessionData = insertChatSessionSchema.parse(req.body);
      
      // Additional validation for userId
      if (!sessionData.userId || sessionData.userId.trim().length === 0) {
        return res.status(400).json({ error: 'Valid user ID is required' });
      }
      
      // Check if user has an active session
      const activeSession = await storage.getActiveChatSession(sessionData.userId);
      if (activeSession && req.body.reuse !== false) {
        return res.json(activeSession);
      }
      
      // Deactivate other sessions before creating new one
      if (activeSession) {
        await storage.deactivateOtherSessions(sessionData.userId, '');
      }
      
      // Create new session
      const session = await storage.createChatSession(sessionData);
      res.json(session);
    } catch (error) {
      console.error('Create chat session error:', error);
      res.status(400).json({ error: error instanceof Error ? error.message : 'Invalid session data' });
    }
  });

  // Get chat session with messages
  app.get('/api/chat/session/:sessionId', chatRateLimit(30, 60000), async (req, res) => {
    try {
      const { limit = 50 } = req.query;
      const sessionWithMessages = await storage.getChatSessionWithMessages(
        req.params.sessionId, 
        parseInt(limit as string)
      );
      
      if (!sessionWithMessages) {
        return res.status(404).json({ error: 'Chat session not found' });
      }
      
      res.json(sessionWithMessages);
    } catch (error) {
      res.status(500).json({ error: 'Failed to fetch chat session' });
    }
  });

  // Update chat session (change persona, context, etc.)
  app.patch('/api/chat/session/:sessionId', chatRateLimit(20, 60000), async (req, res) => {
    try {
      const updates = updateChatSessionSchema.parse(req.body);
      const session = await storage.updateChatSession(req.params.sessionId, updates);
      
      if (!session) {
        return res.status(404).json({ error: 'Chat session not found' });
      }
      
      res.json(session);
    } catch (error) {
      res.status(400).json({ error: error instanceof Error ? error.message : 'Invalid update data' });
    }
  });

  // Get user's chat sessions
  app.get('/api/chat/sessions/:userId', chatRateLimit(30, 60000), async (req, res) => {
    try {
      const sessions = await storage.getUserChatSessions(req.params.userId);
      res.json(sessions);
    } catch (error) {
      res.status(500).json({ error: 'Failed to fetch chat sessions' });
    }
  });

  // Send message and get AI response
  app.post('/api/chat/message', chatRateLimit(20, 60000), async (req, res) => {
    try {
      // Validate OpenAI API key
      if (!process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY.trim().length === 0) {
        return res.status(500).json({ error: 'AI coach is temporarily unavailable. Please try again later.' });
      }

      // Validate and parse message data
      const messageData = insertChatMessageSchema.parse(req.body);
      
      // Additional validation
      if (!messageData.content || messageData.content.trim().length === 0) {
        return res.status(400).json({ error: 'Message content is required' });
      }
      
      if (messageData.content.length > 2000) {
        return res.status(400).json({ error: 'Message too long. Please keep messages under 2000 characters.' });
      }
      
      // Get session for context
      const session = await storage.getChatSession(messageData.sessionId);
      if (!session) {
        return res.status(404).json({ error: 'Chat session not found' });
      }

      // Check if session is active
      if (!session.isActive) {
        return res.status(400).json({ error: 'Chat session is no longer active' });
      }

      // Save user message
      const userMessage = await storage.addChatMessage(messageData);
      
      // Get recent conversation history with proper windowing
      const allMessages = await storage.getChatMessages(messageData.sessionId, 50);
      const windowedMessages = manageConversationWindow(allMessages, 3500); // Leave room for system prompt
      
      // Build context-aware system prompt
      const systemPrompt = await buildCoachingPrompt(
        session.userId, 
        session.coachPersona, 
        messageData.content,
        session.context
      );

      // Prepare conversation messages for OpenAI
      const conversationMessages = [
        { role: 'system' as const, content: systemPrompt },
        ...windowedMessages.map(msg => ({
          role: msg.sender === 'user' ? 'user' as const : 'assistant' as const,
          content: msg.content
        }))
      ];

      // Get AI response from OpenAI with enhanced error handling
      let completion;
      try {
        completion = await openai.chat.completions.create({
          model: "gpt-4o-mini",
          messages: conversationMessages,
          max_tokens: 500,
          temperature: 0.7,
          timeout: 30000, // 30 second timeout
        });
      } catch (openaiError: any) {
        const errorInfo = handleOpenAIError(openaiError);
        return res.status(errorInfo.status).json({ error: errorInfo.message });
      }

      const aiResponseContent = completion.choices[0]?.message?.content;
      
      if (!aiResponseContent) {
        return res.status(500).json({ error: 'AI coach did not provide a response. Please try again.' });
      }

      // Save AI response with enhanced metadata
      const aiMessage = await storage.addChatMessage({
        sessionId: messageData.sessionId,
        sender: 'ai',
        content: aiResponseContent,
        messageType: 'text',
        metadata: JSON.stringify({
          model: 'gpt-4o-mini',
          persona: session.coachPersona,
          tokensUsed: completion.usage?.total_tokens || 0,
          promptTokens: completion.usage?.prompt_tokens || 0,
          completionTokens: completion.usage?.completion_tokens || 0,
          windowedMessages: windowedMessages.length
        })
      });

      // Return both messages
      res.json({
        userMessage,
        aiMessage,
        session,
        usage: {
          totalTokens: completion.usage?.total_tokens || 0,
          messagesInContext: windowedMessages.length
        }
      });

    } catch (error) {
      console.error('Chat message error:', error);
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: 'Invalid message format', details: error.errors });
      }
      res.status(500).json({ error: 'Failed to process chat message. Please try again.' });
    }
  });

  // Quick action endpoint for common coaching requests
  app.post('/api/chat/quick-action', chatRateLimit(15, 60000), async (req, res) => {
    try {
      // Validate OpenAI API key
      if (!process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY.trim().length === 0) {
        return res.status(500).json({ error: 'AI coach is temporarily unavailable. Please try again later.' });
      }

      const { sessionId, action, data } = req.body;
      
      // Input validation
      if (!sessionId || !action) {
        return res.status(400).json({ error: 'Session ID and action are required' });
      }
      
      const session = await storage.getChatSession(sessionId);
      if (!session) {
        return res.status(404).json({ error: 'Chat session not found' });
      }
      
      if (!session.isActive) {
        return res.status(400).json({ error: 'Chat session is no longer active' });
      }

      let quickPrompt = '';
      let actionData = data || {};

      switch (action) {
        case 'motivation':
          quickPrompt = "Give me some motivational words to keep going with my workout today!";
          break;
        case 'form_help':
          quickPrompt = actionData.exercise 
            ? `Help me improve my form for ${actionData.exercise}. What should I focus on?`
            : "I need help with my exercise form. What should I focus on?";
          break;
        case 'rest_time':
          quickPrompt = "How long should I rest between sets for optimal results?";
          break;
        case 'modify_exercise':
          quickPrompt = actionData.exercise
            ? `Can you suggest modifications or alternatives for ${actionData.exercise}?`
            : "Can you suggest some exercise modifications for my current workout?";
          break;
        case 'progress_check':
          quickPrompt = "How am I doing with my fitness progress? Any suggestions for improvement?";
          break;
        case 'next_workout':
          quickPrompt = "What should I focus on in my next workout session?";
          break;
        default:
          return res.status(400).json({ error: 'Unknown quick action' });
      }

      // Process as regular message
      const userMessage = await storage.addChatMessage({
        sessionId,
        sender: 'user',
        content: quickPrompt,
        messageType: 'quick_action',
        metadata: JSON.stringify({ action, originalData: actionData })
      });

      // Get AI response using the same logic as regular messages
      const systemPrompt = await buildCoachingPrompt(
        session.userId, 
        session.coachPersona, 
        quickPrompt,
        session.context
      );

      const completion = await openai.chat.completions.create({
        model: "gpt-4o-mini",
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: quickPrompt }
        ],
        max_tokens: 300,
        temperature: 0.8, // Slightly higher for quick motivational responses
      });

      const aiResponseContent = completion.choices[0]?.message?.content;
      
      if (!aiResponseContent) {
        return res.status(500).json({ error: 'No response from AI coach' });
      }

      const aiMessage = await storage.addChatMessage({
        sessionId,
        sender: 'ai',
        content: aiResponseContent,
        messageType: 'text',
        metadata: JSON.stringify({
          model: 'gpt-4o-mini',
          persona: session.coachPersona,
          quickAction: action,
          tokensUsed: completion.usage?.total_tokens || 0
        })
      });

      res.json({
        userMessage,
        aiMessage,
        action,
        session
      });

    } catch (error) {
      console.error('Quick action error:', error);
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: 'Invalid quick action format', details: error.errors });
      }
      const errorInfo = handleOpenAIError(error);
      res.status(errorInfo.status).json({ error: errorInfo.message });
    }
  });

  // Delete chat session
  app.delete('/api/chat/session/:sessionId', chatRateLimit(10, 60000), async (req, res) => {
    try {
      const sessionId = req.params.sessionId;
      const deleted = await storage.deleteChatSession(sessionId);
      
      if (!deleted) {
        return res.status(404).json({ error: 'Chat session not found' });
      }
      
      res.json({ success: true, message: 'Chat session deleted successfully' });
    } catch (error) {
      console.error('Delete chat session error:', error);
      res.status(500).json({ error: 'Failed to delete chat session' });
    }
  });

  // Get chat statistics (for admin/monitoring)
  app.get('/api/chat/stats/:userId?', chatRateLimit(30, 60000), async (req, res) => {
    try {
      const userId = req.params.userId;
      const stats = await storage.getSessionStats(userId);
      res.json(stats);
    } catch (error) {
      console.error('Get chat stats error:', error);
      res.status(500).json({ error: 'Failed to get chat statistics' });
    }
  });

  // Cleanup old sessions and messages (for maintenance)
  app.post('/api/chat/cleanup', chatRateLimit(5, 300000), async (req, res) => {
    try {
      const { sessionRetentionDays = 30, messageRetentionDays = 90 } = req.body;
      
      // Basic validation
      if (sessionRetentionDays < 1 || messageRetentionDays < 1) {
        return res.status(400).json({ error: 'Retention days must be positive numbers' });
      }
      
      const [deletedSessions, deletedMessages] = await Promise.all([
        storage.cleanupOldSessions(sessionRetentionDays),
        storage.cleanupOldMessages(messageRetentionDays)
      ]);
      
      res.json({
        success: true,
        deletedSessions,
        deletedMessages,
        message: `Cleanup completed: ${deletedSessions} sessions and ${deletedMessages} messages removed`
      });
    } catch (error) {
      console.error('Cleanup error:', error);
      res.status(500).json({ error: 'Failed to perform cleanup' });
    }
  });

  // Periodic cleanup (run every hour)
  setInterval(async () => {
    try {
      await storage.cleanupOldSessions(30); // Remove sessions older than 30 days
      await storage.cleanupOldMessages(90); // Remove messages older than 90 days
      console.log('Periodic cleanup completed');
    } catch (error) {
      console.error('Periodic cleanup error:', error);
    }
  }, 60 * 60 * 1000); // 1 hour

  // NUTRITION API ROUTES

  // Foods API
  
  // GET /api/nutrition/foods - Search foods
  app.get('/api/nutrition/foods', async (req, res) => {
    try {
      const { q } = req.query;
      let foods;
      
      if (q && typeof q === 'string') {
        foods = await storage.searchFoods(q);
      } else {
        foods = await storage.getAllFoods();
      }
      
      res.json(foods);
    } catch (error) {
      console.error('Search foods error:', error);
      res.status(500).json({ error: 'Failed to search foods' });
    }
  });

  // GET /api/nutrition/foods/:id - Get specific food with portions
  app.get('/api/nutrition/foods/:id', async (req, res) => {
    try {
      const { id } = req.params;
      const food = await storage.getFood(id);
      
      if (!food) {
        return res.status(404).json({ error: 'Food not found' });
      }
      
      const portions = await storage.getFoodPortions(id);
      res.json({ ...food, portions });
    } catch (error) {
      console.error('Get food error:', error);
      res.status(500).json({ error: 'Failed to get food' });
    }
  });

  // POST /api/nutrition/foods - Create custom food
  app.post('/api/nutrition/foods', async (req, res) => {
    try {
      const validatedData = insertFoodSchema.parse(req.body);
      const food = await storage.createFood(validatedData);
      
      // Create default 100g portion
      await storage.createFoodPortion({
        foodId: food.id,
        name: '100g',
        gramsEquivalent: 100,
        isDefault: true,
      });
      
      res.status(201).json(food);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: 'Invalid food data', details: error.errors });
      }
      console.error('Create food error:', error);
      res.status(500).json({ error: 'Failed to create food' });
    }
  });

  // Food portion routes
  
  // POST /api/nutrition/foods/:id/portions - Add portion to food
  app.post('/api/nutrition/foods/:id/portions', async (req, res) => {
    try {
      const { id } = req.params;
      const food = await storage.getFood(id);
      
      if (!food) {
        return res.status(404).json({ error: 'Food not found' });
      }
      
      const validatedData = insertFoodPortionSchema.parse({ ...req.body, foodId: id });
      const portion = await storage.createFoodPortion(validatedData);
      
      res.status(201).json(portion);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: 'Invalid portion data', details: error.errors });
      }
      console.error('Create food portion error:', error);
      res.status(500).json({ error: 'Failed to create food portion' });
    }
  });

  // Meals API
  
  // GET /api/nutrition/meals - Get meals by date
  app.get('/api/nutrition/meals', async (req, res) => {
    try {
      const { date, userId } = req.query;
      
      if (!date || !userId) {
        return res.status(400).json({ error: 'Date and userId are required' });
      }
      
      const meals = await storage.getMealsByDate(userId as string, date as string);
      
      // Get meal items for each meal
      const mealsWithItems = await Promise.all(
        meals.map(async (meal) => {
          const items = await storage.getMealItems(meal.id);
          // Get food details for each item
          const itemsWithFood = await Promise.all(
            items.map(async (item) => {
              const food = await storage.getFood(item.foodId);
              return { ...item, food };
            })
          );
          return { ...meal, items: itemsWithFood };
        })
      );
      
      res.json(mealsWithItems);
    } catch (error) {
      console.error('Get meals error:', error);
      res.status(500).json({ error: 'Failed to get meals' });
    }
  });

  // POST /api/nutrition/meals - Create meal
  app.post('/api/nutrition/meals', async (req, res) => {
    try {
      const validatedData = insertMealSchema.parse(req.body);
      const meal = await storage.createMeal(validatedData);
      res.status(201).json(meal);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: 'Invalid meal data', details: error.errors });
      }
      console.error('Create meal error:', error);
      res.status(500).json({ error: 'Failed to create meal' });
    }
  });

  // PATCH /api/nutrition/meals/:id - Update meal
  app.patch('/api/nutrition/meals/:id', async (req, res) => {
    try {
      const { id } = req.params;
      const validatedData = updateMealSchema.parse(req.body);
      
      const updatedMeal = await storage.updateMeal(id, validatedData);
      
      if (!updatedMeal) {
        return res.status(404).json({ error: 'Meal not found' });
      }
      
      res.json(updatedMeal);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: 'Invalid meal data', details: error.errors });
      }
      console.error('Update meal error:', error);
      res.status(500).json({ error: 'Failed to update meal' });
    }
  });

  // DELETE /api/nutrition/meals/:id - Delete meal
  app.delete('/api/nutrition/meals/:id', async (req, res) => {
    try {
      const { id } = req.params;
      const deleted = await storage.deleteMeal(id);
      
      if (!deleted) {
        return res.status(404).json({ error: 'Meal not found' });
      }
      
      res.json({ success: true });
    } catch (error) {
      console.error('Delete meal error:', error);
      res.status(500).json({ error: 'Failed to delete meal' });
    }
  });

  // Meal Items API
  
  // POST /api/nutrition/meals/:id/items - Add item to meal
  app.post('/api/nutrition/meals/:id/items', async (req, res) => {
    try {
      const { id } = req.params;
      const meal = await storage.getMeal(id);
      
      if (!meal) {
        return res.status(404).json({ error: 'Meal not found' });
      }
      
      const validatedData = insertMealItemSchema.parse({ ...req.body, mealId: id });
      const mealItem = await storage.addMealItem(validatedData);
      
      // Get food details
      const food = await storage.getFood(mealItem.foodId);
      res.status(201).json({ ...mealItem, food });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: 'Invalid meal item data', details: error.errors });
      }
      console.error('Add meal item error:', error);
      res.status(500).json({ error: 'Failed to add meal item' });
    }
  });

  // PATCH /api/nutrition/meals/items/:id - Update meal item
  app.patch('/api/nutrition/meals/items/:id', async (req, res) => {
    try {
      const { id } = req.params;
      const validatedData = updateMealItemSchema.parse(req.body);
      
      const updatedItem = await storage.updateMealItem(id, validatedData);
      
      if (!updatedItem) {
        return res.status(404).json({ error: 'Meal item not found' });
      }
      
      // Get food details
      const food = await storage.getFood(updatedItem.foodId);
      res.json({ ...updatedItem, food });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: 'Invalid meal item data', details: error.errors });
      }
      console.error('Update meal item error:', error);
      res.status(500).json({ error: 'Failed to update meal item' });
    }
  });

  // DELETE /api/nutrition/meals/items/:id - Delete meal item
  app.delete('/api/nutrition/meals/items/:id', async (req, res) => {
    try {
      const { id } = req.params;
      const deleted = await storage.deleteMealItem(id);
      
      if (!deleted) {
        return res.status(404).json({ error: 'Meal item not found' });
      }
      
      res.json({ success: true });
    } catch (error) {
      console.error('Delete meal item error:', error);
      res.status(500).json({ error: 'Failed to delete meal item' });
    }
  });

  // Nutrition Analytics API
  
  // GET /api/nutrition/summary - Get daily nutrition summary
  app.get('/api/nutrition/summary', async (req, res) => {
    try {
      const { userId, startDate, endDate } = req.query;
      
      if (!userId) {
        return res.status(400).json({ error: 'UserId is required' });
      }
      
      // Validate date format if provided
      const isValidDate = (dateString: string): boolean => {
        const date = new Date(dateString);
        return !isNaN(date.getTime()) && dateString.match(/^\d{4}-\d{2}-\d{2}$/);
      };
      
      if (startDate && endDate) {
        // Validate date range
        if (!isValidDate(startDate as string) || !isValidDate(endDate as string)) {
          return res.status(400).json({ error: 'Invalid date format. Use YYYY-MM-DD format.' });
        }
        
        if (new Date(startDate as string) > new Date(endDate as string)) {
          return res.status(400).json({ error: 'Start date must be before or equal to end date.' });
        }
        
        // Get range of summaries
        const summaries = await storage.getNutritionSummaryRange(
          userId as string, 
          startDate as string, 
          endDate as string
        );
        res.json(summaries);
      } else {
        // Get today's summary
        const today = new Date().toISOString().split('T')[0];
        let summary = await storage.getDailyNutritionSummary(userId as string, today);
        
        if (!summary) {
          // Create/update summary for today
          summary = await storage.updateDailyNutritionSummary(userId as string, today);
        }
        
        res.json(summary);
      }
    } catch (error) {
      console.error('Get nutrition summary error:', error);
      res.status(500).json({ error: 'Failed to get nutrition summary' });
    }
  });

  // Nutrition Goals API (extends user profile)
  
  // GET /api/nutrition/goals/:userId - Get user's nutrition goals
  app.get('/api/nutrition/goals/:userId', async (req, res) => {
    try {
      const { userId } = req.params;
      const profile = await storage.getUserProfile(userId);
      
      if (!profile) {
        return res.status(404).json({ error: 'User profile not found' });
      }
      
      const goals = {
        calorieTarget: profile.calorieTarget,
        proteinTarget: profile.proteinTarget,
        carbTarget: profile.carbTarget,
        fatTarget: profile.fatTarget,
        dietType: profile.dietType,
        restrictions: profile.restrictions || [],
        preferredCuisines: profile.preferredCuisines || [],
      };
      
      res.json(goals);
    } catch (error) {
      console.error('Get nutrition goals error:', error);
      res.status(500).json({ error: 'Failed to get nutrition goals' });
    }
  });

  // PUT /api/nutrition/goals/:userId - Update user's nutrition goals
  app.put('/api/nutrition/goals/:userId', async (req, res) => {
    try {
      const { userId } = req.params;
      
      // Validate nutrition goal fields
      const goalSchema = z.object({
        calorieTarget: z.number().min(800).max(5000).optional(),
        proteinTarget: z.number().min(20).max(300).optional(),
        carbTarget: z.number().min(50).max(600).optional(),
        fatTarget: z.number().min(20).max(200).optional(),
        dietType: z.enum(["general", "vegetarian", "vegan", "keto", "paleo", "mediterranean", "low_carb"]).optional(),
        restrictions: z.array(z.string()).optional(),
        preferredCuisines: z.array(z.string()).optional(),
      });
      
      const validatedData = goalSchema.parse(req.body);
      const updatedProfile = await storage.updateUserProfile(userId, validatedData);
      
      if (!updatedProfile) {
        return res.status(404).json({ error: 'User profile not found' });
      }
      
      const goals = {
        calorieTarget: updatedProfile.calorieTarget,
        proteinTarget: updatedProfile.proteinTarget,
        carbTarget: updatedProfile.carbTarget,
        fatTarget: updatedProfile.fatTarget,
        dietType: updatedProfile.dietType,
        restrictions: updatedProfile.restrictions || [],
        preferredCuisines: updatedProfile.preferredCuisines || [],
      };
      
      res.json(goals);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: 'Invalid nutrition goals data', details: error.errors });
      }
      console.error('Update nutrition goals error:', error);
      res.status(500).json({ error: 'Failed to update nutrition goals' });
    }
  });

  // Meal Plans API
  
  // GET /api/nutrition/plans/:userId - Get user's meal plans
  app.get('/api/nutrition/plans/:userId', async (req, res) => {
    try {
      const { userId } = req.params;
      const plans = await storage.getUserMealPlans(userId);
      res.json(plans);
    } catch (error) {
      console.error('Get meal plans error:', error);
      res.status(500).json({ error: 'Failed to get meal plans' });
    }
  });

  // GET /api/nutrition/plans/plan/:id - Get specific meal plan with items
  app.get('/api/nutrition/plans/plan/:id', async (req, res) => {
    try {
      const { id } = req.params;
      const plan = await storage.getMealPlan(id);
      
      if (!plan) {
        return res.status(404).json({ error: 'Meal plan not found' });
      }
      
      const items = await storage.getMealPlanItems(id);
      
      // Get food details for each item
      const itemsWithFood = await Promise.all(
        items.map(async (item) => {
          const food = await storage.getFood(item.foodId);
          return { ...item, food };
        })
      );
      
      res.json({ ...plan, items: itemsWithFood });
    } catch (error) {
      console.error('Get meal plan error:', error);
      res.status(500).json({ error: 'Failed to get meal plan' });
    }
  });

  // POST /api/nutrition/plans - Create meal plan
  app.post('/api/nutrition/plans', async (req, res) => {
    try {
      const validatedData = insertMealPlanSchema.parse(req.body);
      const plan = await storage.createMealPlan(validatedData);
      res.status(201).json(plan);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: 'Invalid meal plan data', details: error.errors });
      }
      console.error('Create meal plan error:', error);
      res.status(500).json({ error: 'Failed to create meal plan' });
    }
  });

  // POST /api/nutrition/plans/:id/apply - Apply meal plan to specific date
  app.post('/api/nutrition/plans/:id/apply', async (req, res) => {
    try {
      const { id } = req.params;
      const { userId, date } = req.body;
      
      if (!userId || !date) {
        return res.status(400).json({ error: 'UserId and date are required' });
      }
      
      const plan = await storage.getMealPlan(id);
      if (!plan) {
        return res.status(404).json({ error: 'Meal plan not found' });
      }
      
      const createdMeals = await storage.applyMealPlanToDate(id, userId, date);
      res.json({ success: true, meals: createdMeals });
    } catch (error) {
      console.error('Apply meal plan error:', error);
      res.status(500).json({ error: 'Failed to apply meal plan' });
    }
  });

  // DELETE /api/nutrition/plans/:id - Delete meal plan
  app.delete('/api/nutrition/plans/:id', async (req, res) => {
    try {
      const { id } = req.params;
      const deleted = await storage.deleteMealPlan(id);
      
      if (!deleted) {
        return res.status(404).json({ error: 'Meal plan not found' });
      }
      
      res.json({ success: true });
    } catch (error) {
      console.error('Delete meal plan error:', error);
      res.status(500).json({ error: 'Failed to delete meal plan' });
    }
  });

  // AI Meal Planning API
  
  // Zod schema for AI meal planning request
  const mealPlanGenerationSchema = z.object({
    userId: z.string(),
    prompt: z.string().min(1).max(500),
    targetCalories: z.number().min(800).max(5000).optional(),
    dietType: z.enum(["general", "vegetarian", "vegan", "keto", "paleo", "mediterranean", "low_carb"]).optional(),
    restrictions: z.array(z.string()).default([]),
    preferredCuisines: z.array(z.string()).default([]),
    mealsToInclude: z.array(z.enum(["breakfast", "lunch", "dinner", "snack"])).default(["breakfast", "lunch", "dinner"]),
  });

  // Zod schema for AI meal plan response validation
  const aiMealPlanResponseSchema = z.object({
    name: z.string(),
    description: z.string(),
    totalCalories: z.number(),
    totalProtein: z.number(),
    totalCarbs: z.number(),
    totalFat: z.number(),
    meals: z.array(z.object({
      mealType: z.enum(["breakfast", "lunch", "dinner", "snack"]),
      foods: z.array(z.object({
        name: z.string(),
        grams: z.number(),
        calories: z.number(),
        protein: z.number(),
        carbs: z.number(),
        fat: z.number(),
      }))
    }))
  });

  // Helper function to find or create food in database
  async function findOrCreateFood(foodName: string, nutrition: any): Promise<string> {
    // First try to find existing food by name
    const existingFoods = await storage.searchFoods(foodName);
    const matchingFood = existingFoods.find(food => 
      food.name.toLowerCase() === foodName.toLowerCase()
    );
    
    if (matchingFood) {
      return matchingFood.id;
    }
    
    // Create new food if not found
    const newFood = await storage.createFood({
      name: foodName,
      category: 'other',
      caloriesPerHundredGrams: nutrition.calories,
      proteinPerHundredGrams: nutrition.protein,
      carbsPerHundredGrams: nutrition.carbs,
      fatPerHundredGrams: nutrition.fat,
      fiberPerHundredGrams: 0,
      sodiumPerHundredGrams: 0,
      isUserCreated: true,
      createdBy: 'ai-generated',
      description: 'AI-generated food item',
    });
    
    // Create default portion
    await storage.createFoodPortion({
      foodId: newFood.id,
      name: '100g',
      gramsEquivalent: 100,
      isDefault: true,
    });
    
    return newFood.id;
  }

  // POST /api/nutrition/plan/generate - Generate AI meal plan (rate limited)
  app.post('/api/nutrition/plan/generate', chatRateLimit(5, 60000), async (req, res) => {
    try {
      const validatedData = mealPlanGenerationSchema.parse(req.body);
      const { userId, prompt, targetCalories, dietType, restrictions, preferredCuisines, mealsToInclude } = validatedData;
      
      // Get user profile for personalization
      const userProfile = await storage.getUserProfile(userId);
      const userCalorieTarget = targetCalories || userProfile?.calorieTarget || 2000;
      const userDietType = dietType || userProfile?.dietType || 'general';
      const userRestrictions = restrictions.length > 0 ? restrictions : (userProfile?.restrictions || []);
      const userCuisines = preferredCuisines.length > 0 ? preferredCuisines : (userProfile?.preferredCuisines || []);
      
      // Create OpenAI client
      const openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY
      });
      
      if (!process.env.OPENAI_API_KEY) {
        return res.status(500).json({ error: 'OpenAI API key not configured' });
      }
      
      // Build system prompt for meal planning
      const systemPrompt = `You are a professional nutritionist and meal planning expert. Generate a comprehensive daily meal plan based on the user's requirements.

REQUIREMENTS:
- User request: "${prompt}"
- Target calories: ${userCalorieTarget}
- Diet type: ${userDietType}
- Dietary restrictions: ${userRestrictions.join(', ') || 'None'}
- Preferred cuisines: ${userCuisines.join(', ') || 'Any'}
- Include meals: ${mealsToInclude.join(', ')}

RULES:
1. Provide realistic, achievable meal suggestions
2. Ensure nutritional balance (protein: 15-30%, carbs: 45-65%, fat: 20-35%)
3. Use common foods that can be easily found
4. Respect all dietary restrictions and preferences
5. Keep individual food portions reasonable (10-500g)
6. Aim for the target calorie goal (±10% is acceptable)

RESPONSE FORMAT:
Return a JSON object with this exact structure:
{
  "name": "Brief descriptive name for the meal plan",
  "description": "2-3 sentence description of the plan and its benefits",
  "totalCalories": <estimated total calories as number>,
  "totalProtein": <estimated total protein in grams as number>,
  "totalCarbs": <estimated total carbs in grams as number>,
  "totalFat": <estimated total fat in grams as number>,
  "meals": [
    {
      "mealType": "breakfast|lunch|dinner|snack",
      "foods": [
        {
          "name": "Food name (e.g., 'Chicken Breast', 'Brown Rice')",
          "grams": <amount in grams as number>,
          "calories": <calories per 100g as number>,
          "protein": <protein per 100g as number>,
          "carbs": <carbs per 100g as number>,
          "fat": <fat per 100g as number>
        }
      ]
    }
  ]
}

Respond with ONLY the JSON object, no additional text.`;

      // Call OpenAI API
      const completion = await openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: `Please create a meal plan: ${prompt}` }
        ],
        temperature: 0.7,
        max_tokens: 2000,
      });

      const aiResponse = completion.choices[0]?.message?.content;
      if (!aiResponse) {
        throw new Error('No response from OpenAI');
      }

      // Parse and validate AI response
      let aiMealPlan;
      try {
        const parsedResponse = JSON.parse(aiResponse);
        aiMealPlan = aiMealPlanResponseSchema.parse(parsedResponse);
      } catch (parseError) {
        console.error('Failed to parse AI response:', parseError, 'Response:', aiResponse);
        throw new Error('Invalid AI response format');
      }

      // Create meal plan in database
      const mealPlan = await storage.createMealPlan({
        userId,
        name: aiMealPlan.name,
        description: aiMealPlan.description,
        targetCalories: aiMealPlan.totalCalories,
        targetProtein: aiMealPlan.totalProtein,
        targetCarbs: aiMealPlan.totalCarbs,
        targetFat: aiMealPlan.totalFat,
        isAIGenerated: true,
        generationPrompt: prompt,
        dietType: userDietType,
        restrictions: userRestrictions,
      });

      // Add meal plan items
      for (const meal of aiMealPlan.meals) {
        for (const food of meal.foods) {
          // Find or create the food in our database
          const foodId = await findOrCreateFood(food.name, {
            calories: food.calories,
            protein: food.protein,
            carbs: food.carbs,
            fat: food.fat,
          });

          // Add to meal plan
          await storage.addMealPlanItem({
            mealPlanId: mealPlan.id,
            mealType: meal.mealType,
            foodId,
            grams: food.grams,
            orderInMeal: 0,
          });
        }
      }

      // Return the created meal plan with items
      const planWithItems = await storage.getMealPlanItems(mealPlan.id);
      const itemsWithFood = await Promise.all(
        planWithItems.map(async (item) => {
          const food = await storage.getFood(item.foodId);
          return { ...item, food };
        })
      );

      res.status(201).json({
        ...mealPlan,
        items: itemsWithFood,
        aiGenerated: true,
      });

    } catch (error) {
      const aiError = handleOpenAIError(error);
      console.error('Generate meal plan error:', error);
      res.status(aiError.status).json({ error: aiError.message });
    }
  });

  const httpServer = createServer(app);

  return httpServer;
}
