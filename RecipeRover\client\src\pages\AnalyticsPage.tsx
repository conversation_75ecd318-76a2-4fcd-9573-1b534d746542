import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import Header from "@/components/Header";
import AnalyticsDashboard from "@/components/AnalyticsDashboard";
import AIChatCoach from "@/components/AIChatCoach";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { MessageCircle, X } from "lucide-react";
import { getCurrentUserId } from "@/lib/currentUser";

export default function AnalyticsPage() {
  const userId = getCurrentUserId();
  const [showChat, setShowChat] = useState(false);
  const [chatContext, setChatContext] = useState<any>(null);

  // Get analytics data for chat context
  const { data: analyticsData } = useQuery({
    queryKey: ['analytics', 'overview', userId],
    queryFn: async () => {
      if (!userId) return null;
      const response = await fetch(`/api/analytics/${userId}`);
      return response.ok ? await response.json() : null;
    },
    enabled: !!userId,
  });

  // Update chat context when analytics data changes
  useEffect(() => {
    if (analyticsData) {
      setChatContext({
        analytics: {
          totalWorkouts: analyticsData.totalWorkouts,
          currentStreak: analyticsData.currentStreak,
          workoutConsistency: analyticsData.workoutConsistency,
          totalVolume: analyticsData.totalVolume,
          personalRecords: analyticsData.personalRecords,
          favoriteExercise: analyticsData.favoriteExercise
        }
      });
    }
  }, [analyticsData]);

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* SEO Meta tags */}
      <title>Analytics Dashboard - Circuit Fitness</title>
      <meta name="description" content="Track your fitness progress with comprehensive analytics, AI-powered insights, and personalized coaching recommendations." />
      
      <main className="pt-6">
        <div className="container px-4 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* Main analytics dashboard */}
            <div className="lg:col-span-3">
              <AnalyticsDashboard />
            </div>
            
            {/* AI Coach sidebar */}
            <div className="lg:col-span-1">
              <div className="sticky top-20">
                <Card>
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <MessageCircle className="h-5 w-5 text-primary" />
                        <CardTitle className="text-lg">Progress Coach</CardTitle>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setShowChat(!showChat)}
                        data-testid="toggle-analytics-chat"
                      >
                        {showChat ? <X className="w-4 h-4" /> : <MessageCircle className="w-4 h-4" />}
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    {!showChat ? (
                      <div className="text-center py-4">
                        <p className="text-sm text-muted-foreground mb-3">
                          Get insights and recommendations based on your progress data
                        </p>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => setShowChat(true)}
                        >
                          Chat About Progress
                        </Button>
                      </div>
                    ) : (
                      <AIChatCoach
                        context={chatContext}
                        compact={true}
                        className="max-h-96"
                        data-testid="analytics-chat"
                      />
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}