@tailwind base;
@tailwind components;
@tailwind utilities;

/* LIGHT MODE */
:root {
  --button-outline: rgba(0,0,0, .10);
  --badge-outline: rgba(0,0,0, .05);

  /* Automatic computation of border around primary / danger buttons */
  --opaque-button-border-intensity: -8; /* In terms of percentages */

  /* Backgrounds applied on top of other backgrounds when hovered/active */
  --elevate-1: rgba(0,0,0, .03);
  --elevate-2: rgba(0,0,0, .08);

  --background: 0 0% 98%;

  --foreground: 240 5% 10%;

  --border: 240 8% 85%;

  --card: 240 2% 96%;

  --card-foreground: 240 5% 10%;

  --card-border: 240 6% 92%;

  --sidebar: 240 3% 94%;

  --sidebar-foreground: 240 5% 10%;

  --sidebar-border: 240 6% 90%;

  --sidebar-primary: 0 100% 60%;

  --sidebar-primary-foreground: 0 0% 98%;

  --sidebar-accent: 240 8% 88%;

  --sidebar-accent-foreground: 240 5% 10%;

  --sidebar-ring: 0 100% 60%;

  --popover: 240 4% 90%;

  --popover-foreground: 240 5% 10%;

  --popover-border: 240 8% 86%;

  --primary: 0 100% 60%;

  --primary-foreground: 0 0% 98%;

  --secondary: 240 6% 86%;

  --secondary-foreground: 240 5% 10%;

  --muted: 240 8% 84%;

  --muted-foreground: 240 5% 45%;

  --accent: 240 12% 82%;

  --accent-foreground: 240 5% 10%;

  --destructive: 0 84% 60%;

  --destructive-foreground: 0 0% 98%;

  --input: 240 8% 75%;
  --ring: 0 100% 60%;
  --chart-1: 240 100% 35%;
  --chart-2: 142 76% 36%;
  --chart-3: 25 95% 53%;
  --chart-4: 280 100% 40%;
  --chart-5: 200 95% 45%;

  --font-sans: Inter, sans-serif;
  --font-serif: Georgia, serif;
  --font-mono: JetBrains Mono, monospace;
  --radius: .5rem; /* 8px */
  --shadow-2xs: 0px 1px 2px 0px hsl(240 5% 10% / 0.05);
  --shadow-xs: 0px 1px 3px 0px hsl(240 5% 10% / 0.10);
  --shadow-sm: 0px 1px 3px 0px hsl(240 5% 10% / 0.10), 0px 1px 2px -1px hsl(240 5% 10% / 0.10);
  --shadow: 0px 1px 3px 0px hsl(240 5% 10% / 0.10), 0px 1px 2px -1px hsl(240 5% 10% / 0.10);
  --shadow-md: 0px 4px 6px -1px hsl(240 5% 10% / 0.10), 0px 2px 4px -1px hsl(240 5% 10% / 0.06);
  --shadow-lg: 0px 10px 15px -3px hsl(240 5% 10% / 0.10), 0px 4px 6px -2px hsl(240 5% 10% / 0.05);
  --shadow-xl: 0px 20px 25px -5px hsl(240 5% 10% / 0.10), 0px 10px 10px -5px hsl(240 5% 10% / 0.04);
  --shadow-2xl: 0px 25px 50px -12px hsl(240 5% 10% / 0.25);
  --tracking-normal: 0em;
  --spacing: 0.25rem;

  /* Automatically computed borders - intensity can be controlled by the user by the --opaque-button-border-intensity setting */

  /* Fallback for older browsers */
  --sidebar-primary-border: hsl(var(--sidebar-primary));
  --sidebar-primary-border: hsl(from hsl(var(--sidebar-primary)) h s calc(l + var(--opaque-button-border-intensity)) / alpha);

  /* Fallback for older browsers */
  --sidebar-accent-border: hsl(var(--sidebar-accent));
  --sidebar-accent-border: hsl(from hsl(var(--sidebar-accent)) h s calc(l + var(--opaque-button-border-intensity)) / alpha);

  /* Fallback for older browsers */
  --primary-border: hsl(var(--primary));
  --primary-border: hsl(from hsl(var(--primary)) h s calc(l + var(--opaque-button-border-intensity)) / alpha);

  /* Fallback for older browsers */
  --secondary-border: hsl(var(--secondary));
  --secondary-border: hsl(from hsl(var(--secondary)) h s calc(l + var(--opaque-button-border-intensity)) / alpha);

  /* Fallback for older browsers */
  --muted-border: hsl(var(--muted));
  --muted-border: hsl(from hsl(var(--muted)) h s calc(l + var(--opaque-button-border-intensity)) / alpha);

  /* Fallback for older browsers */
  --accent-border: hsl(var(--accent));
  --accent-border: hsl(from hsl(var(--accent)) h s calc(l + var(--opaque-button-border-intensity)) / alpha);

  /* Fallback for older browsers */
  --destructive-border: hsl(var(--destructive));
  --destructive-border: hsl(from hsl(var(--destructive)) h s calc(l + var(--opaque-button-border-intensity)) / alpha);
}

.dark {
  --button-outline: rgba(255,255,255, .10);
  --badge-outline: rgba(255,255,255, .05);

  --opaque-button-border-intensity: 9;  /* In terms of percentages */

  /* Backgrounds applied on top of other backgrounds when hovered/active */
  --elevate-1: rgba(255,255,255, .04);
  --elevate-2: rgba(255,255,255, .09);

  --background: 240 5% 8%;

  --foreground: 240 2% 90%;

  --border: 240 5% 18%;

  --card: 240 4% 12%;

  --card-foreground: 240 2% 90%;

  --card-border: 240 5% 20%;

  --sidebar: 240 5% 10%;

  --sidebar-foreground: 240 2% 90%;

  --sidebar-border: 240 6% 16%;

  --sidebar-primary: 0 100% 65%;

  --sidebar-primary-foreground: 0 0% 98%;

  --sidebar-accent: 240 8% 18%;

  --sidebar-accent-foreground: 240 2% 90%;

  --sidebar-ring: 0 100% 65%;

  --popover: 240 5% 16%;

  --popover-foreground: 240 2% 90%;

  --popover-border: 240 6% 22%;

  --primary: 0 100% 65%;

  --primary-foreground: 0 0% 98%;

  --secondary: 240 6% 22%;

  --secondary-foreground: 240 2% 90%;

  --muted: 240 8% 20%;

  --muted-foreground: 240 5% 65%;

  --accent: 240 12% 24%;

  --accent-foreground: 240 2% 90%;

  --destructive: 0 84% 60%;

  --destructive-foreground: 0 0% 98%;

  --input: 240 8% 32%;
  --ring: 0 100% 65%;
  --chart-1: 240 100% 75%;
  --chart-2: 142 76% 60%;
  --chart-3: 25 95% 65%;
  --chart-4: 280 100% 70%;
  --chart-5: 200 95% 70%;

  --shadow-2xs: 0px 1px 2px 0px hsl(240 5% 0% / 0.15);
  --shadow-xs: 0px 1px 3px 0px hsl(240 5% 0% / 0.20);
  --shadow-sm: 0px 1px 3px 0px hsl(240 5% 0% / 0.20), 0px 1px 2px -1px hsl(240 5% 0% / 0.15);
  --shadow: 0px 1px 3px 0px hsl(240 5% 0% / 0.20), 0px 1px 2px -1px hsl(240 5% 0% / 0.15);
  --shadow-md: 0px 4px 6px -1px hsl(240 5% 0% / 0.20), 0px 2px 4px -1px hsl(240 5% 0% / 0.12);
  --shadow-lg: 0px 10px 15px -3px hsl(240 5% 0% / 0.20), 0px 4px 6px -2px hsl(240 5% 0% / 0.10);
  --shadow-xl: 0px 20px 25px -5px hsl(240 5% 0% / 0.20), 0px 10px 10px -5px hsl(240 5% 0% / 0.08);
  --shadow-2xl: 0px 25px 50px -12px hsl(240 5% 0% / 0.35);

}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
  }
}

/**
 * Using the elevate system.
 * Automatic contrast adjustment.
 *
 * <element className="hover-elevate" />
 * <element className="active-elevate-2" />
 *
 * // Using the tailwind utility when a data attribute is "on"
 * <element className="toggle-elevate data-[state=on]:toggle-elevated" />
 * // Or manually controlling the toggle state
 * <element className="toggle-elevate toggle-elevated" />
 *
 * Elevation systems have to handle many states.
 * - not-hovered, vs. hovered vs. active  (three mutually exclusive states)
 * - toggled or not
 * - focused or not (this is not handled with these utilities)
 *
 * Even without handling focused or not, this is six possible combinations that
 * need to be distinguished from eachother visually.
 */
@layer utilities {

  /* Hide ugly search cancel button in Chrome until we can style it properly */
  input[type="search"]::-webkit-search-cancel-button {
    @apply hidden;
  }

  /* Placeholder styling for contentEditable div */
  [contenteditable][data-placeholder]:empty::before {
    content: attr(data-placeholder);
    color: hsl(var(--muted-foreground));
    pointer-events: none;
  }

  /* .no-default-hover-elevate/no-default-active-elevate is an escape hatch so consumers of
   * buttons/badges can remove the automatic brightness adjustment on interactions
   * and program their own. */
  .no-default-hover-elevate {}

  .no-default-active-elevate {}


  /**
   * Toggleable backgrounds go behind the content. Hoverable/active goes on top.
   * This way they can stack/compound. Both will overlap the parent's borders!
   * So borders will be automatically adjusted both on toggle, and hover/active,
   * and they will be compounded.
   */
  .toggle-elevate::before,
  .toggle-elevate-2::before {
    content: "";
    pointer-events: none;
    position: absolute;
    inset: 0px;
    /*border-radius: inherit;   match rounded corners */
    border-radius: inherit;
    z-index: -1;
    /* sits behind content but above backdrop */
  }

  .toggle-elevate.toggle-elevated::before {
    background-color: var(--elevate-2);
  }

  /* If there's a 1px border, adjust the inset so that it covers that parent's border */
  .border.toggle-elevate::before {
    inset: -1px;
  }

  /* Does not work on elements with overflow:hidden! */
  .hover-elevate:not(.no-default-hover-elevate),
  .active-elevate:not(.no-default-active-elevate),
  .hover-elevate-2:not(.no-default-hover-elevate),
  .active-elevate-2:not(.no-default-active-elevate) {
    position: relative;
    z-index: 0;
  }

  .hover-elevate:not(.no-default-hover-elevate)::after,
  .active-elevate:not(.no-default-active-elevate)::after,
  .hover-elevate-2:not(.no-default-hover-elevate)::after,
  .active-elevate-2:not(.no-default-active-elevate)::after {
    content: "";
    pointer-events: none;
    position: absolute;
    inset: 0px;
    /*border-radius: inherit;   match rounded corners */
    border-radius: inherit;
    z-index: 999;
    /* sits in front of content */
  }

  .hover-elevate:hover:not(.no-default-hover-elevate)::after,
  .active-elevate:active:not(.no-default-active-elevate)::after {
    background-color: var(--elevate-1);
  }

  .hover-elevate-2:hover:not(.no-default-hover-elevate)::after,
  .active-elevate-2:active:not(.no-default-active-elevate)::after {
    background-color: var(--elevate-2);
  }

  /* If there's a 1px border, adjust the inset so that it covers that parent's border */
  .border.hover-elevate:not(.no-hover-interaction-elevate)::after,
  .border.active-elevate:not(.no-active-interaction-elevate)::after,
  .border.hover-elevate-2:not(.no-hover-interaction-elevate)::after,
  .border.active-elevate-2:not(.no-active-interaction-elevate)::after,
  .border.hover-elevate:not(.no-hover-interaction-elevate)::after {
    inset: -1px;
  }
}