import { useState, useEffect } from "react";
import { useQ<PERSON>y, useMutation, useQueryClient } from "@tanstack/react-query";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { apiRequest } from "@/lib/queryClient";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { 
  User, 
  Target, 
  Activity, 
  Dumbbell, 
  Calendar, 
  Scale, 
  Ruler, 
  CheckCircle2,
  Edit3,
  Plus,
  Trash2,
  TrendingUp,
  Trophy,
  Heart,
  Zap,
  Building
} from "lucide-react";

// Shared user ID mechanism
import { getCurrentUserId } from "@/lib/currentUser";

// Form schemas based on our backend schema - using coercion for HTML form inputs
const userProfileSchema = z.object({
  userId: z.string(),
  age: z.coerce.number().min(13).max(120).optional(), // Made optional to match backend schema
  height: z.coerce.number().min(100).max(250).optional(), // cm - coerced and optional
  weight: z.coerce.number().min(30).max(300).optional(), // kg - coerced and optional
  fitnessLevel: z.enum(["beginner", "intermediate", "advanced"]),
  goals: z.array(z.enum(["weight_loss", "muscle_gain", "strength", "endurance", "general_fitness"])).min(1),
  targetWeight: z.coerce.number().min(30).max(300).optional(),
  targetDate: z.union([z.date(), z.string().transform(str => str ? new Date(str) : undefined)]).optional(),
  preferredWorkoutDuration: z.coerce.number().min(15).max(180).default(45),
  weeklyWorkoutFrequency: z.coerce.number().min(1).max(7).default(3),
  availableEquipment: z.array(z.string()).default([]),
  activityLevel: z.enum(["sedentary", "lightly_active", "moderately_active", "very_active"]),
});

type UserProfileData = z.infer<typeof userProfileSchema>;

interface UserProfile extends UserProfileData {
  id: string;
  isComplete: boolean;
  createdAt: string;
  updatedAt: string;
}

// Equipment options
const EQUIPMENT_OPTIONS = [
  { value: "dumbbells", label: "Dumbbells", icon: Dumbbell },
  { value: "barbell", label: "Barbell", icon: Dumbbell },
  { value: "kettlebell", label: "Kettlebells", icon: Dumbbell },
  { value: "resistance_bands", label: "Resistance Bands", icon: Activity },
  { value: "pull_up_bar", label: "Pull-up Bar", icon: Activity },
  { value: "bench", label: "Bench", icon: Building },
  { value: "yoga_mat", label: "Yoga Mat", icon: Activity },
  { value: "treadmill", label: "Treadmill", icon: Activity },
  { value: "stationary_bike", label: "Stationary Bike", icon: Activity },
  { value: "bodyweight", label: "Bodyweight Only", icon: User },
];

// Goal options with descriptions
const GOAL_OPTIONS = [
  { 
    value: "weight_loss", 
    label: "Weight Loss", 
    description: "Lose body fat and achieve a healthier weight",
    icon: TrendingUp,
    color: "text-red-500"
  },
  { 
    value: "muscle_gain", 
    label: "Muscle Gain", 
    description: "Build lean muscle mass and strength",
    icon: Dumbbell,
    color: "text-blue-500"
  },
  { 
    value: "strength", 
    label: "Strength", 
    description: "Increase overall strength and power",
    icon: Trophy,
    color: "text-yellow-500"
  },
  { 
    value: "endurance", 
    label: "Endurance", 
    description: "Improve cardiovascular fitness and stamina",
    icon: Heart,
    color: "text-green-500"
  },
  { 
    value: "general_fitness", 
    label: "General Fitness", 
    description: "Overall health and well-being",
    icon: Zap,
    color: "text-purple-500"
  },
];

export default function UserProfile() {
  const { toast } = useToast();
  const currentUserId = getCurrentUserId();
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState("basic");
  const [isEditing, setIsEditing] = useState(false);

  // Query for user profile
  const { data: profile, isLoading } = useQuery<UserProfile | null>({
    queryKey: ['/api/profile', currentUserId],
    queryFn: async () => {
      try {
        const response = await fetch(`/api/profile/${currentUserId}`);
        if (response.status === 404) return null;
        if (!response.ok) throw new Error('Failed to fetch profile');
        return await response.json();
      } catch (error) {
        console.error('Profile fetch error:', error);
        return null;
      }
    },
  });

  // Query for profile completion status
  const { data: profileStatus } = useQuery({
    queryKey: ['/api/profile', currentUserId, 'status'],
    queryFn: async () => {
      const response = await fetch(`/api/profile/${currentUserId}/status`);
      return await response.json();
    },
  });

  // Form setup
  const form = useForm<UserProfileData>({
    resolver: zodResolver(userProfileSchema),
    defaultValues: {
      userId: currentUserId,
      age: 25,
      height: 170,
      weight: 70,
      fitnessLevel: "beginner",
      goals: [],
      preferredWorkoutDuration: 45,
      weeklyWorkoutFrequency: 3,
      availableEquipment: [],
      activityLevel: "lightly_active",
    },
  });

  // Set form values when profile loads
  useEffect(() => {
    if (profile) {
      form.reset({
        userId: profile.userId,
        age: profile.age || 25,
        height: Number(profile.height) || 170,
        weight: Number(profile.weight) || 70,
        fitnessLevel: profile.fitnessLevel,
        goals: profile.goals,
        targetWeight: profile.targetWeight ? Number(profile.targetWeight) : undefined,
        targetDate: profile.targetDate ? new Date(profile.targetDate) : undefined,
        preferredWorkoutDuration: profile.preferredWorkoutDuration,
        weeklyWorkoutFrequency: profile.weeklyWorkoutFrequency,
        availableEquipment: profile.availableEquipment,
        activityLevel: profile.activityLevel,
      });
      setIsEditing(false);
    } else {
      setIsEditing(true);
    }
  }, [profile, form]);

  // Mutations
  const createProfileMutation = useMutation({
    mutationFn: async (data: UserProfileData) => {
      const response = await apiRequest('POST', '/api/profile', data);
      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/profile', currentUserId] });
      queryClient.invalidateQueries({ queryKey: ['/api/profile', currentUserId, 'status'] });
      setIsEditing(false);
      toast({ title: "Profile created!", description: "Your fitness profile has been set up successfully." });
    },
    onError: () => {
      toast({ title: "Error", description: "Failed to create profile", variant: "destructive" });
    },
  });

  const updateProfileMutation = useMutation({
    mutationFn: async (data: Partial<UserProfileData>) => {
      const response = await apiRequest('PATCH', `/api/profile/${currentUserId}`, data);
      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/profile', currentUserId] });
      queryClient.invalidateQueries({ queryKey: ['/api/profile', currentUserId, 'status'] });
      setIsEditing(false);
      toast({ title: "Profile updated!", description: "Your changes have been saved successfully." });
    },
    onError: () => {
      toast({ title: "Error", description: "Failed to update profile", variant: "destructive" });
    },
  });

  const handleSubmit = (data: UserProfileData) => {
    if (profile) {
      updateProfileMutation.mutate(data);
    } else {
      createProfileMutation.mutate(data);
    }
  };

  const getCompletionColor = (percentage: number) => {
    if (percentage >= 80) return "text-green-500";
    if (percentage >= 60) return "text-yellow-500";
    return "text-red-500";
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6 max-w-4xl">
      {/* Header */}
      <Card className="mb-6">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="flex items-center justify-center w-12 h-12 rounded-full bg-primary text-primary-foreground">
                <User className="h-6 w-6" />
              </div>
              <div>
                <CardTitle className="text-2xl">Fitness Profile</CardTitle>
                <p className="text-muted-foreground">
                  {profile ? "Manage your fitness goals and preferences" : "Set up your personalized fitness journey"}
                </p>
              </div>
            </div>
            
            {profile && (
              <div className="flex items-center gap-4">
                {profileStatus && (
                  <div className="flex items-center gap-2">
                    <div className="text-right">
                      <div className="text-sm font-medium">Profile Completion</div>
                      <div className={`text-sm ${getCompletionColor(profileStatus.completionPercentage)}`}>
                        {profileStatus.completionPercentage}%
                      </div>
                    </div>
                    <Progress 
                      value={profileStatus.completionPercentage} 
                      className="w-20" 
                      data-testid="progress-profile-completion"
                    />
                  </div>
                )}
                
                <Button
                  variant={isEditing ? "outline" : "default"}
                  onClick={() => setIsEditing(!isEditing)}
                  data-testid="button-toggle-edit"
                >
                  <Edit3 className="h-4 w-4 mr-2" />
                  {isEditing ? "Cancel" : "Edit Profile"}
                </Button>
              </div>
            )}
          </div>
        </CardHeader>
      </Card>

      {/* Profile Form */}
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="basic" data-testid="tab-basic">Basic Info</TabsTrigger>
              <TabsTrigger value="goals" data-testid="tab-goals">Goals</TabsTrigger>
              <TabsTrigger value="preferences" data-testid="tab-preferences">Preferences</TabsTrigger>
              <TabsTrigger value="equipment" data-testid="tab-equipment">Equipment</TabsTrigger>
            </TabsList>

            {/* Basic Information Tab */}
            <TabsContent value="basic" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5" />
                    Personal Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <FormField
                      control={form.control}
                      name="age"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Age</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              placeholder="25"
                              {...field}
                              onChange={(e) => field.onChange(Number(e.target.value))}
                              disabled={!isEditing}
                              data-testid="input-age"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="height"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Height (cm)</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <Input
                                type="number"
                                placeholder="170"
                                {...field}
                                onChange={(e) => field.onChange(Number(e.target.value))}
                                disabled={!isEditing}
                                data-testid="input-height"
                              />
                              <Ruler className="absolute right-3 top-3 h-4 w-4 text-muted-foreground" />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="weight"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Weight (kg)</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <Input
                                type="number"
                                placeholder="70"
                                {...field}
                                onChange={(e) => field.onChange(Number(e.target.value))}
                                disabled={!isEditing}
                                data-testid="input-weight"
                              />
                              <Scale className="absolute right-3 top-3 h-4 w-4 text-muted-foreground" />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="fitnessLevel"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Current Fitness Level</FormLabel>
                          <Select 
                            onValueChange={field.onChange} 
                            defaultValue={field.value}
                            disabled={!isEditing}
                          >
                            <FormControl>
                              <SelectTrigger data-testid="select-fitness-level">
                                <SelectValue placeholder="Select your fitness level" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="beginner">Beginner - New to exercise</SelectItem>
                              <SelectItem value="intermediate">Intermediate - Regular exercise</SelectItem>
                              <SelectItem value="advanced">Advanced - Very experienced</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="activityLevel"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Daily Activity Level</FormLabel>
                          <Select 
                            onValueChange={field.onChange} 
                            defaultValue={field.value}
                            disabled={!isEditing}
                          >
                            <FormControl>
                              <SelectTrigger data-testid="select-activity-level">
                                <SelectValue placeholder="Select your activity level" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="sedentary">Sedentary - Desk job, minimal activity</SelectItem>
                              <SelectItem value="lightly_active">Lightly Active - Light exercise 1-3 days/week</SelectItem>
                              <SelectItem value="moderately_active">Moderately Active - Moderate exercise 3-5 days/week</SelectItem>
                              <SelectItem value="very_active">Very Active - Heavy exercise 6-7 days/week</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Goals Tab */}
            <TabsContent value="goals" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="h-5 w-5" />
                    Fitness Goals
                  </CardTitle>
                  <p className="text-muted-foreground">
                    Select one or more goals to personalize your workout recommendations
                  </p>
                </CardHeader>
                <CardContent className="space-y-6">
                  <FormField
                    control={form.control}
                    name="goals"
                    render={() => (
                      <FormItem>
                        <FormLabel>Primary Goals</FormLabel>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {GOAL_OPTIONS.map((goal) => (
                            <FormField
                              key={goal.value}
                              control={form.control}
                              name="goals"
                              render={({ field }) => {
                                const IconComponent = goal.icon;
                                return (
                                  <FormItem
                                    key={goal.value}
                                    className="flex flex-row items-start space-x-3 space-y-0"
                                  >
                                    <FormControl>
                                      <Checkbox
                                        checked={field.value?.includes(goal.value as any)}
                                        onCheckedChange={(checked) => {
                                          return checked
                                            ? field.onChange([...field.value, goal.value])
                                            : field.onChange(
                                                field.value?.filter(
                                                  (value) => value !== goal.value
                                                )
                                              )
                                        }}
                                        disabled={!isEditing}
                                        data-testid={`checkbox-goal-${goal.value}`}
                                      />
                                    </FormControl>
                                    <div className="grid gap-1.5 leading-none">
                                      <div className="flex items-center gap-2">
                                        <IconComponent className={`h-4 w-4 ${goal.color}`} />
                                        <Label className="font-medium">
                                          {goal.label}
                                        </Label>
                                      </div>
                                      <p className="text-xs text-muted-foreground">
                                        {goal.description}
                                      </p>
                                    </div>
                                  </FormItem>
                                )
                              }}
                            />
                          ))}
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <Separator />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="targetWeight"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Target Weight (kg) - Optional</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              placeholder="Target weight"
                              {...field}
                              onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : undefined)}
                              disabled={!isEditing}
                              data-testid="input-target-weight"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="targetDate"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Target Date - Optional</FormLabel>
                          <FormControl>
                            <Input
                              type="date"
                              {...field}
                              value={field.value ? field.value.toISOString().split('T')[0] : ''}
                              onChange={(e) => field.onChange(e.target.value ? new Date(e.target.value) : undefined)}
                              disabled={!isEditing}
                              data-testid="input-target-date"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Preferences Tab */}
            <TabsContent value="preferences" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="h-5 w-5" />
                    Workout Preferences
                  </CardTitle>
                  <p className="text-muted-foreground">
                    Tell us about your workout schedule and preferences
                  </p>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="preferredWorkoutDuration"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Preferred Workout Duration (minutes)</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <Input
                                type="number"
                                placeholder="45"
                                {...field}
                                onChange={(e) => field.onChange(Number(e.target.value))}
                                disabled={!isEditing}
                                data-testid="input-workout-duration"
                              />
                              <Calendar className="absolute right-3 top-3 h-4 w-4 text-muted-foreground" />
                            </div>
                          </FormControl>
                          <p className="text-xs text-muted-foreground">
                            How long do you typically want to work out?
                          </p>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="weeklyWorkoutFrequency"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Weekly Workout Frequency</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <Input
                                type="number"
                                placeholder="3"
                                min="1"
                                max="7"
                                {...field}
                                onChange={(e) => field.onChange(Number(e.target.value))}
                                disabled={!isEditing}
                                data-testid="input-workout-frequency"
                              />
                              <Activity className="absolute right-3 top-3 h-4 w-4 text-muted-foreground" />
                            </div>
                          </FormControl>
                          <p className="text-xs text-muted-foreground">
                            How many days per week do you want to work out?
                          </p>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Equipment Tab */}
            <TabsContent value="equipment" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Dumbbell className="h-5 w-5" />
                    Available Equipment
                  </CardTitle>
                  <p className="text-muted-foreground">
                    Select the equipment you have access to for personalized workout recommendations
                  </p>
                </CardHeader>
                <CardContent>
                  <FormField
                    control={form.control}
                    name="availableEquipment"
                    render={() => (
                      <FormItem>
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                          {EQUIPMENT_OPTIONS.map((equipment) => (
                            <FormField
                              key={equipment.value}
                              control={form.control}
                              name="availableEquipment"
                              render={({ field }) => {
                                const IconComponent = equipment.icon;
                                return (
                                  <FormItem
                                    key={equipment.value}
                                    className="flex flex-row items-start space-x-3 space-y-0"
                                  >
                                    <FormControl>
                                      <Checkbox
                                        checked={field.value?.includes(equipment.value)}
                                        onCheckedChange={(checked) => {
                                          return checked
                                            ? field.onChange([...field.value, equipment.value])
                                            : field.onChange(
                                                field.value?.filter(
                                                  (value) => value !== equipment.value
                                                )
                                              )
                                        }}
                                        disabled={!isEditing}
                                        data-testid={`checkbox-equipment-${equipment.value}`}
                                      />
                                    </FormControl>
                                    <div className="grid gap-1.5 leading-none">
                                      <div className="flex items-center gap-2">
                                        <IconComponent className="h-4 w-4 text-primary" />
                                        <Label className="text-sm font-medium">
                                          {equipment.label}
                                        </Label>
                                      </div>
                                    </div>
                                  </FormItem>
                                )
                              }}
                            />
                          ))}
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* Submit Button */}
          {isEditing && (
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <p className="text-sm text-muted-foreground">
                    {profile ? "Update your profile information" : "Create your fitness profile to get started"}
                  </p>
                  <div className="flex gap-2">
                    {profile && (
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setIsEditing(false)}
                        data-testid="button-cancel-edit"
                      >
                        Cancel
                      </Button>
                    )}
                    <Button
                      type="submit"
                      disabled={createProfileMutation.isPending || updateProfileMutation.isPending}
                      data-testid="button-save-profile"
                    >
                      <CheckCircle2 className="h-4 w-4 mr-2" />
                      {createProfileMutation.isPending || updateProfileMutation.isPending
                        ? "Saving..."
                        : profile
                        ? "Update Profile"
                        : "Create Profile"}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </form>
      </Form>
    </div>
  );
}