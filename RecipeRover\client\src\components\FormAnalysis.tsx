import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { CheckCircle, AlertTriangle, Target, Brain, Loader2, AlertCircle, MessageCircle } from 'lucide-react';
import CameraCapture from './CameraCapture';
import AIChatCoach from './AIChatCoach';

interface FormAnalysisResult {
  rating: number;
  feedback: string;
  corrections: string[];
  safetyNotes: string[];
}

export default function FormAnalysis() {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<FormAnalysisResult | null>(null);
  const [selectedExercise, setSelectedExercise] = useState('Squat');
  const [error, setError] = useState<string>('');
  const [showChat, setShowChat] = useState(false);
  const [chatContext, setChatContext] = useState<any>(null);

  const exercises = ['Squat', 'Deadlift', 'Bench Press', 'Push-up', 'Plank', 'Lunge'];

  const analyzeForm = async (imageData: string) => {
    setIsAnalyzing(true);
    setError('');
    
    try {
      const response = await fetch('/api/vision/analyze-form', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          image: imageData,
          exercise: selectedExercise,
        }),
      });

      if (!response.ok) {
        throw new Error('Analysis failed');
      }

      const result = await response.json();
      setAnalysisResult(result);
      console.log('Form analysis result:', result);
    } catch (error) {
      console.error('Form analysis error:', error);
      setError('Failed to analyze form. Please try again.');
      setAnalysisResult(null);
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Update chat context when analysis results change
  useEffect(() => {
    if (analysisResult) {
      setChatContext({
        formAnalysis: {
          exercise: selectedExercise,
          rating: analysisResult.rating,
          feedback: analysisResult.feedback,
          corrections: analysisResult.corrections,
          safetyNotes: analysisResult.safetyNotes
        }
      });
    }
  }, [analysisResult, selectedExercise]);

  const handleLiveAnalysis = async (imageData: string) => {
    // Throttled live analysis - only analyze if not currently analyzing
    if (!isAnalyzing) {
      await analyzeForm(imageData);
    }
  };

  const getFormRatingColor = (rating: number) => {
    if (rating >= 8) return 'text-green-500';
    if (rating >= 6) return 'text-yellow-500';
    return 'text-red-500';
  };

  const getFormRatingLabel = (rating: number) => {
    if (rating >= 8) return 'Excellent';
    if (rating >= 6) return 'Good';
    return 'Needs Work';
  };

  return (
    <section className="py-12 px-4 lg:px-8 bg-muted/30" id="form-analysis">
      <div className="container mx-auto max-w-6xl">
        <div className="text-center mb-8">
          <h2 className="text-3xl lg:text-4xl font-bold mb-4">
            AI-Powered <span className="text-primary">Form Analysis</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Get real-time feedback on your exercise form using advanced computer vision technology.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-8">
          {/* Exercise Selection & Camera */}
          <div className="space-y-6">
            {/* Exercise Selection */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5 text-primary" />
                  Select Exercise
                </CardTitle>
                <CardDescription>Choose the exercise you want to analyze</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-2">
                  {exercises.map((exercise) => (
                    <Button
                      key={exercise}
                      variant={selectedExercise === exercise ? "default" : "outline"}
                      onClick={() => setSelectedExercise(exercise)}
                      className="justify-start"
                      data-testid={`button-select-${exercise.toLowerCase().replace(' ', '-')}`}
                    >
                      {exercise}
                    </Button>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Camera Component */}
            <CameraCapture
              onImageCapture={analyzeForm}
              onVideoFrame={handleLiveAnalysis}
              isAnalyzing={isAnalyzing}
              exercise={selectedExercise}
            />
          </div>

          {/* Analysis Results */}
          <div className="space-y-6">
            {error && (
              <Card className="border-destructive/20 bg-destructive/5">
                <CardContent className="flex items-center justify-center py-12">
                  <div className="text-center">
                    <AlertCircle className="h-8 w-8 text-destructive mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2 text-destructive">Analysis Failed</h3>
                    <p className="text-muted-foreground mb-4">{error}</p>
                    <Button onClick={() => setError('')} variant="outline">
                      Try Again
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {isAnalyzing && (
              <Card className="border-primary/20 bg-primary/5">
                <CardContent className="flex items-center justify-center py-12">
                  <div className="text-center">
                    <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">Analyzing Your Form</h3>
                    <p className="text-muted-foreground">
                      AI is reviewing your {selectedExercise} technique...
                    </p>
                  </div>
                </CardContent>
              </Card>
            )}

            {analysisResult && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Brain className="h-5 w-5 text-primary" />
                    Form Analysis Results
                  </CardTitle>
                  <CardDescription>AI feedback for your {selectedExercise}</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Overall Rating */}
                  <div className="text-center">
                    <div className={`text-4xl font-bold mb-2 ${getFormRatingColor(analysisResult.rating)}`}>
                      {analysisResult.rating}/10
                    </div>
                    <Badge variant="secondary" className="mb-4">
                      {getFormRatingLabel(analysisResult.rating)}
                    </Badge>
                    <Progress 
                      value={analysisResult.rating * 10} 
                      className="max-w-xs mx-auto"
                    />
                  </div>

                  {/* Feedback */}
                  <div>
                    <h4 className="font-semibold mb-2 flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      Overall Feedback
                    </h4>
                    <p className="text-sm text-muted-foreground bg-muted/50 p-3 rounded-lg">
                      {analysisResult.feedback}
                    </p>
                  </div>

                  {/* Corrections */}
                  {analysisResult.corrections && analysisResult.corrections.length > 0 && (
                    <div>
                      <h4 className="font-semibold mb-2 flex items-center gap-2">
                        <Target className="h-4 w-4 text-blue-500" />
                        Areas for Improvement
                      </h4>
                      <ul className="space-y-2">
                        {analysisResult.corrections.map((correction, idx) => (
                          <li key={idx} className="text-sm flex items-start gap-2">
                            <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 shrink-0"></div>
                            <span>{correction}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {/* Safety Notes */}
                  {analysisResult.safetyNotes && analysisResult.safetyNotes.length > 0 && (
                    <div>
                      <h4 className="font-semibold mb-2 flex items-center gap-2">
                        <AlertTriangle className="h-4 w-4 text-yellow-500" />
                        Safety Reminders
                      </h4>
                      <ul className="space-y-2">
                        {analysisResult.safetyNotes.map((note, idx) => (
                          <li key={idx} className="text-sm flex items-start gap-2">
                            <AlertTriangle className="h-3 w-3 text-yellow-500 mt-1 shrink-0" />
                            <span>{note}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {!analysisResult && !isAnalyzing && (
              <Card className="border-dashed">
                <CardContent className="flex items-center justify-center py-12">
                  <div className="text-center">
                    <Brain className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">Ready for Analysis</h3>
                    <p className="text-muted-foreground">
                      Start your camera and capture an image or begin live analysis
                    </p>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* AI Coach Integration */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <MessageCircle className="h-5 w-5 text-primary" />
                    <CardTitle className="text-lg">AI Form Coach</CardTitle>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowChat(!showChat)}
                    data-testid="toggle-form-chat"
                  >
                    {showChat ? 'Hide' : 'Show'} Chat
                  </Button>
                </div>
                <CardDescription>
                  Get personalized coaching based on your form analysis results
                </CardDescription>
              </CardHeader>
              {showChat && (
                <CardContent>
                  <AIChatCoach
                    context={chatContext}
                    className="max-h-96"
                    data-testid="form-analysis-chat"
                  />
                </CardContent>
              )}
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
}