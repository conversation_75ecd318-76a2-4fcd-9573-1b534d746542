# Fit Circuit — Technical System Overview

This document provides a complete technical overview of the Fit Circuit fitness coaching application for senior engineers: current architecture, APIs, storage, operational practices, integration points, and future plans.

---

## 1. System Architecture & Components

### 1.1 High-level Architecture
- Next.js 15 (App Router) + React 19
- Tailwind CSS v4 + shadcn/ui component primitives; Recharts for visualization
- Server routes under `app/api/*` (Edge-compatible patterns avoided on purpose due to file I/O)
- File-based persistence in `data/` (JSON) with atomic writes; easily swappable to DB
- Path alias: `@/*` → `./src/*`

### 1.2 Major Subsystems
- AI Integration
  - Perplexity Chat/Completions proxy (structured JSON prompts for meal/workout plans and progress review)
  - OpenAI Vision analysis endpoint (on-demand user-triggered image analysis)
- Progress Tracking
  - Client UI for adding logs (date, weight, notes), local persistence mirrored to file DB through API
  - Line chart visualization and KPI cards
- Plan Management
  - Simple CRUD via API backed by file store (create/list/get/delete)
- Calculators
  - BMI, BMR (Mifflin–St Jeor), TDEE calculators via HTTP GET endpoints
- App Shell & Navigation
  - Top bar + responsive sidebar Nav; consistent layout and tokens

### 1.3 Data Flow (simplified)
```mermaid
flowchart TD
  A[User UI] -->|POST logs| B[/API: progress/logs/]
  B -->|write JSON| C[(data/progress.json)]
  A -->|GET logs| B
  A -->|POST AI prompt| D[/API: ai/*/]
  D -->|Server fetch| E[(Perplexity/OpenAI)]
  E -->|JSON| D --> A
  A -->|GET/POST| F[/API: plans/]
  F -->|read/write| G[(data/plans.json)]
  A -->|GET| H[/API: calc/*/]
```

### 1.4 AuthN/AuthZ
- Current: None (public dev prototype). API keys used server-side only.
- Planned:
  - Supabase Auth (email/password + OAuth), JWT-based sessions, RLS-backed data isolation
  - Role model: `user` (default), `coach` (manage plans/clients), `admin`
  - Protected routes and server actions gated by session; per-user data partitions

### 1.5 File-based Storage & Migration Path
- Location: `data/` at repo root
  - `progress.json`: `{ nextId: number, logs: { id, date, weight?, notes? }[] }`
  - `plans.json`: `{ plans: { id, title, goal, level, durationWeeks, content? }[] }`
- Atomic write strategy: write to `*.tmp` + `rename()` for durability
- Migration path to DB (e.g., Postgres/Supabase):
  1) Create SQL tables (see §2.2 future schema)
  2) Write import script to read JSON and insert batched rows
  3) Switch repository functions in `src/lib/*` to DB-backed implementations
  4) Enable Supabase RLS to scope data by `user_id`
  5) Keep a one-time migration command and fallback export script

---

## 2. Technical Implementation Details

### 2.1 API Endpoints (current)
- Base: `/api/*` (Next.js App Router route handlers)

AI (Perplexity/OpenAI)
- POST `/api/ai/chat`
  - Request:
    ```json
    { "messages": [{"role":"system|user|assistant","content":"..."}], "model":"pplx-70b-online", "temperature":0.2 }
    ```
  - Response: passthrough of Perplexity JSON (includes `choices[0].message.content`)
- POST `/api/ai/meal-plan`
  - Request:
    ```json
    { "calories": 2200, "goal": "cut|maintain|bulk", "diet": "string?", "allergies": ["string"] }
    ```
  - Response (normalized JSON):
    ```json
    { "days": [{"day":1, "meals":[{"name":"","calories":0,"protein":0,"carbs":0,"fat":0,"ingredients":[],"instructions":""}]}], "totalCaloriesPerDay": 0 }
    ```
- POST `/api/ai/workout-plan`
  - Request:
    ```json
    { "goal": "fat_loss|muscle_gain|strength|endurance", "daysPerWeek": 4, "equipment": ["string"], "experience": "beginner|intermediate|advanced" }
    ```
  - Response (normalized JSON):
    ```json
    { "weeks": [{"week":1, "days":[{"day":1, "title":"","exercises":[{"name":"","sets":0,"reps":"","rest":""}]}]}] }
    ```
- POST `/api/ai/progress-review`
  - Request:
    ```json
    { "logs": [{"date":"YYYY-MM-DD","weight": 0, "notes":""}], "metrics": {"k":"v"} }
    ```
  - Response (normalized JSON):
    ```json
    { "summary":"", "insights": [""], "nextWeekFocus": [""], "redFlags": [""] }
    ```
- POST `/api/vision/analyze`
  - Request:
    ```json
    { "image": "data:... or https://...", "prompt": "optional" }
    ```
  - Response: passthrough OpenAI chat/completions JSON

Progress Logs
- GET `/api/progress/logs` → `{ logs: ProgressLog[] }`
- POST `/api/progress/logs` → create one
  - Request:
    ```json
    { "date":"YYYY-MM-DD", "weight": 185.2, "notes": "string?" }
    ```
  - Response: created log with `id`
- DELETE `/api/progress/logs` → clear all
- DELETE `/api/progress/logs/:id` → `{ ok: boolean }`

Plans
- GET `/api/plans` → `{ plans: Plan[] }`
- POST `/api/plans` → create
  - Request:
    ```json
    { "title":"", "goal":"", "level":"", "durationWeeks": 4, "content": {} }
    ```
- GET `/api/plans/:id` → plan
- DELETE `/api/plans/:id` → `{ ok: boolean }`

Calculators
- GET `/api/calc/bmi?weightKg=85&heightCm=180` → `{ bmi: number, category: string }`
- GET `/api/calc/bmr?weightKg=85&heightCm=180&age=30&sex=male|female` → `{ bmr: number }`
- GET `/api/calc/tdee?bmr=1900&activity=sedentary|light|moderate|active|very_active` → `{ tdee, activity, factor }`

### 2.2 Database Schema
Current (file JSON)
- `progress.json`
  ```json
  { "nextId": 1, "logs": [{ "id": 1, "date": "YYYY-MM-DD", "weight": 185.2, "notes": "" }] }
  ```
- `plans.json`
  ```json
  { "plans": [{ "id":"abc123", "title":"", "goal":"", "level":"", "durationWeeks":4, "content": {} }] }
  ```

Future (SQL example)
- Tables
  - users(id pk, email unique, role)
  - progress_logs(id pk, user_id fk, date, weight, notes, created_at)
  - plans(id pk, owner_user_id fk, title, goal, level, duration_weeks, content jsonb, created_at)
- Indexes: (user_id, date) on progress_logs; (owner_user_id) on plans
- RLS: user can only read/write their own rows; `coach` role can manage assigned clients

### 2.3 Component Hierarchy & State Patterns
- `app/layout.tsx` → AppShell (top bar + sidebar) → pages
- Pages (`app/*/page.tsx`):
  - `ai-tools`: local state for chat log; server fetch to AI APIs
  - `progress`: local state for inputs; `localStorage` mirror; fetch to /api/progress/* for persistence; Recharts for chart
  - `fitness-plans`: local state + fetch to /api/plans for seeding/browsing
- Patterns: co-located UI state; server communication via `fetch` to route handlers; minimal client logic, server validates

### 2.4 Error Handling & Validation
- Zod validation for AI generation inputs and server APIs (meal-plan, workout-plan, progress-review, plans)
- Structured error responses: `{ error: string }`
- Fail-closed strategy on missing env keys for AI endpoints (500)
- Chart empty state: UI fallback when no data

### 2.5 Security & Secrets
- API keys via `.env.local`; never exposed to client-side code
  - `PERPLEXITY_API_KEY` for Perplexity endpoints
  - `OPENAI_API_KEY` for Vision
- Server-only fetch to external APIs; client receives normalized JSON
- Planned: Supabase Auth + RLS for multi-user isolation
- CORS: dev defaults (Next.js); production to be tightened via platform

---

## 3. Operational Systems

### 3.1 Development Workflow
- Install: `npm install`
- Run: `npm run dev` (Turbopack)
- Build: `npm run build` → `npm start`
- Code organization: `app/*` for routes, `src/lib/*` for persistence/services, `src/components/*` for UI

### 3.2 Testing Strategy
- Unit: Vitest for `src/lib/*` modules (file DB and plan store)
- Integration/API: Supertest (or Next test utilities) for `app/api/*`
- E2E: Playwright for user flows (AI calls mocked)
- CI: GitHub Actions (Node LTS matrix) running lint, type checks, unit/integration tests

### 3.3 Deployment Pipeline
- Recommended: Vercel (Next.js native) or containerized Node (Docker)
- Environments: `.env` per environment; set secrets via platform UI
- Edge functions avoided for endpoints needing file I/O; move persistence to DB before using Edge

### 3.4 Monitoring & Logging
- Server logs: Next.js runtime logs
- Recommended add-ons: Sentry (errors, traces), Logflare/BetterStack (logs), Vercel Analytics (RUM)
- Health endpoints: can add `/api/health` returning build info and status

### 3.5 Performance Optimization
- Client: code-splitting by route; minimal bundle for each page; avoid large client libraries
- Server: cache stable AI prompts if needed; consider rate limiting
- Images: prefer Next Image for hosted assets (Vision uses user-supplied data URLs)
- DB future: use indexes, keyset pagination for logs/plans

---

## 4. Integration Points

### 4.1 Third-party Services
- Perplexity AI: `https://api.perplexity.ai/chat/completions`
- OpenAI: `https://api.openai.com/v1/chat/completions` (Vision model e.g., `gpt-4o-mini`)

### 4.2 External API Dependencies & Fallbacks
- Network or key errors → `{ error: string }` JSON; UI shows raw JSON or friendly messages
- Consider adding exponential backoff + user-facing retries for production

### 4.3 Future Integrations
- Supabase (Auth + Postgres + Storage)
- Payments (Stripe) for subscriptions/coaching plans
- Email (Resend) for onboarding and plan delivery

---

## 5. Business Logic & Features

### 5.1 Feature Matrix (Current → Planned)
- AI Tools
  - [x] Chat (Perplexity proxy)
  - [x] Meal Plan JSON
  - [x] Workout Plan JSON
  - [x] Progress Review JSON
  - [x] Vision Analyze (OpenAI)
  - [ ] Streaming responses, tools for adherence/macros
- Progress
  - [x] Add logs, chart, KPIs
  - [x] File persistence via API
  - [ ] Per-user histories (Auth)
  - [ ] Advanced analytics (rolling averages, goals)
- Plans
  - [x] CRUD (file store)
  - [ ] Detailed view, favorites, calendar export
  - [ ] Templates & marketplace
- Calculators
  - [x] BMI/BMR/TDEE
  - [ ] Macro calculator and goal planning

### 5.2 User Journeys
- New user: lands on `/`, clicks into AI Tools/Progress/Plans; can use calculators without auth
- Returning user (future): sign-in → personalized data, saved logs/plans, recommendations
- Coach (future): manage clients, assign plans, review progress summaries

### 5.3 Persistence & Backup
- File store for local/dev: `data/*.json` committed to `.gitignore` in production
- Backups: periodic copy or export scripts; production to rely on managed DB snapshots (Supabase PITR)

### 5.4 Scalability & Bottlenecks
- File I/O is single-host and not concurrent-safe at scale → migrate to DB for multi-user
- AI endpoints bound by provider rate limits → add circuit breakers and caching if needed
- Client-heavy pages are small; Recharts is light-weight; SSR hydration cost acceptable

---

## 6. Appendix
- Path Alias: `@/*` → `./src/*` (see `tsconfig.json`)
- Styling Tokens in `app/globals.css` (primary scarlet `#EF233C`, etc.)
- AppShell in `src/components/app-shell.tsx` provides the product frame
- Replace file stores in `src/lib/*` when moving to DB; keep function signatures similar to ease swap

