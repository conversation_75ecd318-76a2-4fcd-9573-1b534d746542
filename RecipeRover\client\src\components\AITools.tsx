import { <PERSON>, CardContent, Card<PERSON><PERSON>cription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { <PERSON>Circle, ChefHat, <PERSON><PERSON><PERSON>, TrendingUp, Send } from "lucide-react";
import { useState } from "react";

export default function AITools() {
  const [chatMessage, setChatMessage] = useState("");
  const [chatHistory, setChatHistory] = useState([
    { role: "assistant", content: "Hi! I'm your AI fitness coach. How can I help you today?" }
  ]);

  const handleSendMessage = () => {
    if (!chatMessage.trim()) return;
    
    setChatHistory(prev => [
      ...prev,
      { role: "user", content: chatMessage },
      { role: "assistant", content: "Great question! Let me analyze that for you..." }
    ]);
    setChatMessage("");
    console.log("Message sent:", chatMessage);
  };

  const generateMealPlan = () => {
    console.log("Generating AI meal plan...");
  };

  const generateWorkoutPlan = () => {
    console.log("Generating AI workout plan...");
  };

  const reviewProgress = () => {
    console.log("Analyzing progress with AI...");
  };

  return (
    <section className="py-24 px-4 lg:px-8 bg-muted/30" id="ai-tools">
      <div className="container mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold mb-6">
            AI-Powered <span className="text-primary">Coaching Tools</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Advanced AI technology that works alongside our expert trainers to provide 
            personalized guidance and insights.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-8">
          {/* AI Chat */}
          <Card className="lg:col-span-1">
            <CardHeader>
              <div className="flex items-center gap-3">
                <MessageCircle className="h-6 w-6 text-primary" />
                <div>
                  <CardTitle>AI Coach Chat</CardTitle>
                  <CardDescription>Get instant answers and personalized guidance</CardDescription>
                </div>
                <Badge variant="secondary">Live</Badge>
              </div>
            </CardHeader>
            <CardContent>
              {/* Chat History */}
              <div className="h-64 bg-muted/30 rounded-lg p-4 mb-4 overflow-y-auto">
                {chatHistory.map((msg, idx) => (
                  <div key={idx} className={`mb-3 ${msg.role === 'user' ? 'text-right' : 'text-left'}`}>
                    <div className={`inline-block p-3 rounded-lg max-w-[80%] ${
                      msg.role === 'user' 
                        ? 'bg-primary text-primary-foreground' 
                        : 'bg-background border'
                    }`}>
                      {msg.content}
                    </div>
                  </div>
                ))}
              </div>
              
              {/* Chat Input */}
              <div className="flex gap-2">
                <Textarea 
                  placeholder="Ask your AI coach anything..."
                  value={chatMessage}
                  onChange={(e) => setChatMessage(e.target.value)}
                  className="resize-none"
                  rows={2}
                  data-testid="input-chat-message"
                />
                <Button 
                  onClick={handleSendMessage}
                  size="icon"
                  className="shrink-0"
                  data-testid="button-send-message"
                >
                  <Send className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* AI Tools Grid */}
          <div className="space-y-6">
            <Card className="hover-elevate">
              <CardHeader>
                <div className="flex items-center gap-3">
                  <ChefHat className="h-6 w-6 text-primary" />
                  <div>
                    <CardTitle className="text-lg">AI Meal Planner</CardTitle>
                    <CardDescription>Generate personalized nutrition plans</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  Create custom meal plans based on your goals, dietary preferences, and restrictions.
                </p>
                <Button onClick={generateMealPlan} className="w-full" data-testid="button-generate-meal-plan">
                  Generate Meal Plan
                </Button>
              </CardContent>
            </Card>

            <Card className="hover-elevate">
              <CardHeader>
                <div className="flex items-center gap-3">
                  <Dumbbell className="h-6 w-6 text-primary" />
                  <div>
                    <CardTitle className="text-lg">AI Workout Builder</CardTitle>
                    <CardDescription>Create optimized training programs</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  Generate workout plans tailored to your fitness level, goals, and available equipment.
                </p>
                <Button onClick={generateWorkoutPlan} className="w-full" data-testid="button-generate-workout-plan">
                  Create Workout Plan
                </Button>
              </CardContent>
            </Card>

            <Card className="hover-elevate">
              <CardHeader>
                <div className="flex items-center gap-3">
                  <TrendingUp className="h-6 w-6 text-primary" />
                  <div>
                    <CardTitle className="text-lg">Progress Analyzer</CardTitle>
                    <CardDescription>AI-powered progress insights</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  Get detailed analysis of your progress with actionable recommendations.
                </p>
                <Button onClick={reviewProgress} className="w-full" data-testid="button-review-progress">
                  Analyze Progress
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
}