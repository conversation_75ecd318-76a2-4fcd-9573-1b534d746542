# Fit Circuit — Professional Fitness Coaching App (Next.js)

A modern, production‑ready fitness coaching web app. Built with Next.js (App Router), Tailwind v4, and shadcn/ui. Includes AI tools (chat, plans, progress review), progress tracking, plan storage, and nutrition/energy calculators — with a simple dev setup and file‑based persistence so you can run everything locally without extra services.

---

## Table of Contents
- Overview
- Features at a Glance
- Tech Stack
- Project Structure
- Getting Started
- Environment Variables
- Running & Building
- UI Routes
- API Reference
  - AI: Chat, Meal Plan, Workout Plan, Progress Review, Vision Analyze
  - Progress Logs (file DB)
  - Plans (file DB)
  - Calculators: BMI, BMR, TDEE
- Data Storage
- Theming & Design System
- Notes & Decisions
- Roadmap
- Troubleshooting

---

## Overview
Fit Circuit aims to be a “badass professional” coaching app where you can see visual progress quickly and plug in AI features over time. It’s currently wired with a clean App Shell, polished pages, and working APIs. You can run locally with zero cloud dependencies (AI endpoints require keys; the rest works offline).

---

## Features at a Glance
- App Shell: Top bar, responsive sidebar, consistent layout
- Pages:
  - Home: Simple landing with big links
  - AI Tools: Chat with AI, generate meal/workout plans
  - Progress: Add logs, view weight trend chart, AI weekly review, optional Vision analysis
  - Fitness Plans: Browse/seed plans and get AI recommendation
- APIs:
  - AI (Perplexity/OpenAI)
  - Progress Logs CRUD (file‑based JSON)
  - Plans CRUD (file‑based JSON)
  - Calculators: BMI, BMR, TDEE
- Design: Scarlet/Red + Black/White theme, shadcn/ui components, Tailwind v4 tokens

---

## Tech Stack
- Framework: Next.js 15 (App Router), React 19
- Styling: Tailwind CSS v4, shadcn/ui, Lucide icons
- Charts: Recharts
- AI: Perplexity API (chat/completions) and OpenAI API (vision)
- Language: TypeScript
- Storage: Local JSON files under `data/` (no DB required for local dev)

---

## Project Structure
```
app/
  layout.tsx            # Root layout (html/body + AppShell)
  globals.css           # Global styles + theme tokens
  page.tsx              # Simple home with 3 links
  ai-tools/page.tsx     # AI chat + meal/workout generators
  progress/page.tsx     # Logs + chart + AI review + Vision analyze
  fitness-plans/page.tsx# Plan cards + AI recommendation
  api/
    ai/
      chat/route.ts           # Perplexity chat proxy
      meal-plan/route.ts      # Perplexity JSON meal plan
      progress-review/route.ts# Perplexity JSON progress analysis
      workout-plan/route.ts   # Perplexity JSON workout plan
    vision/analyze/route.ts   # OpenAI vision analysis
    progress/
      logs/route.ts           # GET all, POST create, DELETE clear
      logs/[id]/route.ts      # DELETE one
    plans/
      route.ts                # GET all, POST create
      [id]/route.ts           # GET one, DELETE
    calc/
      bmi/route.ts            # GET bmi
      bmr/route.ts            # GET bmr
      tdee/route.ts           # GET tdee
src/
  components/app-shell.tsx    # Top bar + sidebar navigation
  components/ui/*             # shadcn/ui components
  lib/filedb.ts               # JSON file persistence for progress logs
  lib/planstore.ts            # JSON file persistence for plans
```

Path alias: `@/*` resolves to `./src/*` (see `tsconfig.json`).

---

## Getting Started
1) Requirements
- Node 20+
- npm 10+

2) Install
```
npm install
```

3) Dev
```
npm run dev
# open http://localhost:3000
```

4) Build & Start
```
npm run build
npm run start
```

---

## Environment Variables
Create `.env.local` in the project root as needed.

- Perplexity (for AI chat/meal/workout/progress review)
```
PERPLEXITY_API_KEY=sk-...
```
- OpenAI (for vision image analysis only)
```
OPENAI_API_KEY=sk-...
```
The app UI will still load without these; only AI calls will return 400/500 if keys are missing.

---

## Running & Building
- Dev: `npm run dev` (Turbopack)
- Production build: `npm run build` → `npm run start`

If you access via LAN, Next.js may warn about allowed dev origins. This is safe in dev; see Next docs to configure `allowedDevOrigins` later.

---

## UI Routes
- `/` — Simple landing with big links (Replit‑style)
- `/ai-tools` — AI coach chat + generate meal/workout plans
- `/progress` — Add logs (date/weight), see weight trend chart, run AI review, optional vision analyze
- `/fitness-plans` — Browse sample plans; create/seed more via API; AI recommendation button

---

## API Reference
All endpoints are served under the App Router (`app/api/*`). Examples use `curl`.

### AI
- POST `/api/ai/chat`
  - Body: `{ messages: [{role, content}], model?, temperature? }`
  - Env: `PERPLEXITY_API_KEY`
  - Example:
```
curl -s -X POST http://localhost:3000/api/ai/chat \
  -H "Content-Type: application/json" \
  -d '{"messages":[{"role":"user","content":"Give me a push day workout idea"}]}'
```

- POST `/api/ai/meal-plan`
  - Body: `{ calories?, goal: "cut|maintain|bulk", diet?, allergies?[] }`
  - Returns strict JSON structure

- POST `/api/ai/workout-plan`
  - Body: `{ goal, daysPerWeek, equipment?, experience }`
  - Returns strict JSON structure

- POST `/api/ai/progress-review`
  - Body: `{ logs: [{date, weight?, notes?}], metrics? }`
  - Returns JSON summary/insights

- POST `/api/vision/analyze`
  - Body: `{ image: <data URL or remote URL>, prompt? }`
  - Env: `OPENAI_API_KEY`

### Progress Logs (file DB)
- GET `/api/progress/logs` → `{ logs: [...] }`
- POST `/api/progress/logs` → create one
  - Body: `{ date: string, weight?: number, notes?: string }`
- DELETE `/api/progress/logs` → clear all
- DELETE `/api/progress/logs/:id` → delete one by id

Examples:
```
# List
curl -s http://localhost:3000/api/progress/logs
# Create
curl -s -X POST http://localhost:3000/api/progress/logs \
  -H "Content-Type: application/json" \
  -d '{"date":"2025-09-18","weight":185.2}'
# Delete one
curl -s -X DELETE http://localhost:3000/api/progress/logs/1
```

### Plans (file DB)
- GET `/api/plans` → `{ plans: [...] }`
- POST `/api/plans` → create
  - Body: `{ title, goal, level, durationWeeks, content? }`
- GET `/api/plans/:id` → one plan
- DELETE `/api/plans/:id` → remove

Examples:
```
# Seed
curl -s -X POST http://localhost:3000/api/plans \
  -H "Content-Type: application/json" \
  -d '{"title":"Beginner Full Body","goal":"general","level":"beginner","durationWeeks":4}'
# List
curl -s http://localhost:3000/api/plans
```

### Calculators
- GET `/api/calc/bmi?weightKg=85&heightCm=180` → `{ bmi, category }`
- GET `/api/calc/bmr?weightKg=85&heightCm=180&age=30&sex=male` → `{ bmr }`
- GET `/api/calc/tdee?bmr=1900&activity=moderate` → `{ tdee, activity, factor }`

---

## Data Storage
- Location: `data/` folder at repo root
  - `progress.json` — `{ nextId, logs: [...] }`
  - `plans.json` — `{ plans: [...] }`
- Implementations:
  - `src/lib/filedb.ts` — progress logs (atomic write via temp file + rename)
  - `src/lib/planstore.ts` — plans
- Safe for local dev demos; swap to a real DB later (Supabase/Postgres) with similar interfaces.

---

## Theming & Design System
- Global styles and theme tokens in `app/globals.css`
- Scarlet/Red as primary (`#EF233C`) with a clean black/white base
- shadcn/ui components for consistent, accessible UI
- Recharts for charts; color driven by CSS variables (`--chart-1`)
- AppShell (`src/components/app-shell.tsx`) provides the professional frame (top bar + sidebar)

---

## Notes & Decisions
- React 19 compatibility: Tremor currently targets React 18; we chose Recharts to avoid peer dependency issues.
- Kept UI clean and minimal; can layer in advanced visuals later.
- All AI endpoints proxy through server routes to keep keys off the client.

---

## Roadmap (Suggested Next Steps)
- Auth & Profiles (Supabase Auth)
- Real database (Supabase/Postgres) + RLS
- Plan details view + favorites + calendar export
- Better AI UX: streaming chat, tool use for macros, adherence scoring
- Mobile polish: gestures, sticky CTA, bottom nav (if desired)
- Dark mode toggle and per‑component theming cleanup
- E2E tests (Playwright) and API tests (Vitest) with CI

---

## Troubleshooting
- Routes 404 in dev: hard refresh or open in new tab/incognito; ensure files exist under `app/` (not `src/app/` only)
- AI endpoints 400/500: ensure `PERPLEXITY_API_KEY` or `OPENAI_API_KEY` are set in `.env.local`
- Windows dev: if LAN access warns about dev origins, it’s informational during dev
- Styling off: ensure Tailwind v4 is installed and `app/globals.css` is imported in `app/layout.tsx`

---

Happy shipping! Hand this README to any engineer and they can run the app, use the APIs, and understand the architecture in minutes.
